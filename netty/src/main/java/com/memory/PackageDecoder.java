package com.memory;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.util.AttributeKey;
import io.netty.util.internal.ObjectUtil;

import java.nio.charset.Charset;
import java.util.List;

import static com.memory.TcpSnoopServerHandler.clients;

public class PackageDecoder extends ByteToMessageDecoder {
    private final Charset charset;


    public PackageDecoder() {
        this(Charset.defaultCharset());
    }

    public PackageDecoder(Charset charset) {
        this.charset = (Charset) ObjectUtil.checkNotNull(charset, "charset");
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        in.markReaderIndex();
        String payload = in.toString(this.charset);
        System.out.println("decode payload:::" + payload);

        if ((payload.contains("*") && payload.contains("#"))) {
            in.skipBytes(in.readableBytes());
            out.add(payload);
        } else {
            // todo 本地测试时下面一行注释掉
            String deviceNo = ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get().toString();
            // todo 本地测试时下面一行写入假的设备id如“7024000001”
            ClientInfo client = clients.get(deviceNo);
            // 正常模式
            if (client.getModel().equals(1)) {
                if (payload.endsWith("16")) {
                    in.skipBytes(in.readableBytes());
                    out.add(payload);
                } else in.resetReaderIndex();
            } else {
                // 透传模式
                in.skipBytes(in.readableBytes());
                out.add(payload);
            }
        }
    }
}
