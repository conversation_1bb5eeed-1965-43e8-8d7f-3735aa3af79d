/*
 * Copyright 2022 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package com.memory;

import com.alibaba.fastjson.JSONObject;
import com.taosdata.jdbc.utils.StringUtils;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.SelfSignedCertificate;
import io.netty.util.CharsetUtil;
import lombok.Data;
import redis.clients.jedis.Jedis;

import javax.net.ssl.SSLException;
import java.math.BigDecimal;
import java.security.cert.CertificateException;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;

import static com.memory.Redis.jedisPool;
import static com.memory.TcpSnoopServerHandler.*;

/**
 * Some useful methods for server side.
 */
@Data
public final class ServerUtil {
    private String deviceNo;
    private String cmd;
    private String uuid;
    public static ExecutorService executorService = new ThreadPoolExecutor(2,
            Runtime.getRuntime().availableProcessors() * 2,
            60L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy());
    //
    private static final boolean SSL = System.getProperty("ssl") != null;
    //
    public static ExecutorService cachedThreadPool = Executors.newFixedThreadPool(3);

    private ServerUtil() {
    }

    public static SslContext buildSslContext() throws CertificateException, SSLException {
        if (!SSL) {
            return null;
        }
        SelfSignedCertificate ssc = new SelfSignedCertificate();
        return SslContextBuilder
                .forServer(ssc.certificate(), ssc.privateKey())
                .build();
    }

    public static void transfer(HashMap<String, Object> tdata) {
        try (Jedis redis = jedisPool.getResource()) {
            long spq = redis.lpush("share-push-queue", JSONObject.toJSONString(tdata));
            System.out.println("share-push-queue:" + spq);
            //
            String deviceNo = tdata.get("deviceNo").toString();
            // 下线
            if (tdata.get("type").equals("4")) {
                redis.hset("cache:" + deviceNo, "offlineTime", tdata.get("offlineTime").toString());
            }
            // 上线
            else if (tdata.get("type").equals("3")) {
                redis.hset("cache:" + deviceNo, "onlineTime", tdata.get("onlineTime").toString());
                redis.hset("cache:" + deviceNo, "offlineTime", "");
                redis.hset("cache:" + deviceNo, "heartTime", tdata.get("onlineTime").toString());
            } else if (tdata.get("type").equals("0")) {
                redis.hset("cache:" + deviceNo, "heartTime", tdata.get("heartTime").toString());
            }
        }

        executorService.submit(() -> {
            HttpTransfer.post(pushUrl, tdata);
        });
    }

    public static void transfer2(HashMap<String, Object> tdata) {
        try (Jedis redis = jedisPool.getResource()) {
            long spq = redis.lpush("share-push-queue", JSONObject.toJSONString(tdata));
            System.out.println("share-push-queue:" + spq);
            //
            String deviceNo = tdata.get("deviceNo").toString();
            //将data 和 通道号 同步到redis
            redis.hset("cache:" + deviceNo,tdata.get("passage").toString(),JSONObject.toJSONString(tdata.get("data")));
            // 下线
            if (tdata.get("type").equals("4")) {
                redis.hset("cache:" + deviceNo, "offlineTime", tdata.get("offlineTime").toString());
            }
            // 上线
            else if (tdata.get("type").equals("3")) {
                redis.hset("cache:" + deviceNo, "onlineTime", tdata.get("onlineTime").toString());
                redis.hset("cache:" + deviceNo, "offlineTime", "");
                redis.hset("cache:" + deviceNo, "heartTime", tdata.get("onlineTime").toString());
            } else if (tdata.get("type").equals("0")) {
                redis.hset("cache:" + deviceNo, "heartTime", tdata.get("heartTime").toString());
            }
        }

//        executorService.submit(() -> {
//            HttpTransfer.post(pushUrl, tdata);
//        });
    }

    public static HashMap<String, Object> sendCmd(String deviceNo, String cmd) {
        ServerUtil su = new ServerUtil();
        su.setDeviceNo(deviceNo);
        su.setCmd(cmd.replaceAll(" ", ""));
        return sendCmd(su);
    }

    public static void main(String[] args) {
        Future<?> a = cachedThreadPool.submit((Runnable) () -> {

            long time = System.currentTimeMillis();
            while (true) {
                long now = System.currentTimeMillis();
                if (now - time > 5000) {
                    break;
                }
            }

        });
        try {
            System.out.println(a.get());
            cachedThreadPool.shutdown();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static HashMap<String, Object> sendCmd(ServerUtil su) {
        // 创建一个HashMap用于存储返回结果
        HashMap<String, Object> cmd = new HashMap<>();
        try {
            // 检查命令和设备编号是否为空
            if (StringUtils.isEmpty(su.getCmd().trim()) || StringUtils.isEmpty(su.getDeviceNo().trim())) {
                cmd.put("message", "ERROR");
                return cmd;
            }
            // 从clients获取对应的ClientInfo对象
            ClientInfo ci = clients.get(su.getDeviceNo());
            // 检查ClientInfo对象是否有效
            if (null == ci || 1 != ci.getStatus() || !ci.getChannel().isOpen()) {
                // 如果ClientInfo对象不为空且通道不为空，则关闭通道
                if (null != ci && null != ci.getChannel())
                    closeChannel(ci.getChannel(), null, "sendCmd");
                cmd.put("message", "OFFLINE");
                return cmd;
            }
            // 如果UUID为空，则生成一个新的UUID
            if (StringUtils.isEmpty(su.getUuid())) {
                su.setUuid(UUID.randomUUID().toString().replaceAll("-", ""));
            }
            // 根据命令前缀设置模型
            if (su.getCmd().trim().startsWith("68")) {
                ci.setModel(1);
            } else {
                // 15秒透传仙人模式
                if (!ci.getModel().equals(2)) {
                    ci.setModel(2);
                    // 提交一个线程用于在15秒后将模型重置为1
                    cachedThreadPool.submit(() -> {
                        long time = System.currentTimeMillis();
                        while (ci.getModel().equals(2)) {
                            long now = System.currentTimeMillis();
                            if (now - time > 15000) {
                                ci.setModel(1);
                                break;
                            }
                        }
                    });
                }
            }
            // 将命令写入通道并发送
            ChannelFuture cf = ci.getChannel().writeAndFlush(Unpooled.copiedBuffer(su.getCmd(), CharsetUtil.UTF_8));
            // 添加监听器用于处理发送结果
            // 为cf对象添加一个监听器，用于监听channelFuture的状态变化
            cf.addListener(channelFuture -> {
                // 检查channelFuture是否已完成
                boolean isDone = channelFuture.isDone();
                // 检查channelFuture是否成功
                boolean isSuccess = channelFuture.isSuccess();
                // 检查channelFuture是否可取消
                boolean isCancellable = channelFuture.isCancellable();
                // 检查channelFuture是否已取消
                boolean isCancelled = channelFuture.isCancelled();
                // 初始化命令状态为"未知"
                String cmdStatus = "未知";
                // 如果channelFuture已完成，将命令状态设置为"DONE"
                if (isDone) cmdStatus = "DONE";
                // 如果channelFuture成功，将命令状态设置为"SUCCESS"
                if (isSuccess) cmdStatus = "SUCCESS";
                // 如果channelFuture可取消或已取消，将命令状态设置为"CANCEL"
                if (isCancellable || isCancelled) cmdStatus = "CANCEL";
                // 创建一个HashMap用于存储传输数据
                HashMap<String, Object> tdata = new HashMap<>();
                // 将设备编号添加到HashMap中
                tdata.put("deviceNo", su.getDeviceNo());
                // 将命令数据添加到HashMap中
                tdata.put("data", su.getCmd());
                // 将命令状态添加到HashMap中
                tdata.put("cmdStatus", cmdStatus);
                // 将类型设置为2并添加到HashMap中
                tdata.put("type", 2);
                // 将UUID添加到HashMap中
                tdata.put("uuid", su.getUuid());
                // 调用transfer方法传输数据
                transfer(tdata);
            });
            cmd.put("message", "SUCCESS");
            cmd.put("uuid", su.getUuid());
            return cmd;
        } catch (Exception ex) {
            ex.printStackTrace();
            cmd.put("message", "error");
            return cmd;
        }
    }

    public static ChannelFuture sendCmdReturnChannelFuture(String deviceNo, String cmd) {
        try {
            if (StringUtils.isEmpty(cmd.trim()) || StringUtils.isEmpty(deviceNo.trim())) {
                return null;
            }
            ClientInfo ci = clients.get(deviceNo);
            if (null == ci || 1 != ci.getStatus() || !ci.getChannel().isOpen()) {
                if (null != ci && null != ci.getChannel())
                    closeChannel(ci.getChannel(), null, "sendCmdReturnChannelFuture");
                return null;
            }
            ChannelFuture cf = ci.getChannel().writeAndFlush(Unpooled.copiedBuffer(cmd, CharsetUtil.UTF_8));
            return cf;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public static void cacheCmdSend(String deviceNo) {
        System.out.println("空闲60秒下发缓存命令 " + deviceNo);
        try (Jedis redis = Redis.jedisPool.getResource()) {
            long cmdLen = redis.llen(deviceNo);
            if (cmdLen <= 0) return;
            List<String> cmds = redis.lrange(deviceNo, 0, 1);
            try {
                String cmd = cmds.get(0);
                System.out.println("空闲60秒下发缓存命令【下发】 " + deviceNo + " " + cmd);
                ChannelFuture cf = sendCmdReturnChannelFuture(deviceNo, cmd);
                if (null == cf) return;
                cf.addListener(channelFuture -> {
                    if (channelFuture.isSuccess()) {
                        System.out.println("空闲60秒下发缓存命令【成功】 " + deviceNo + " " + cmd);
                        redis.lrem(deviceNo, 1, cmd);
                        Thread.sleep(100);
                        cacheCmdSend(deviceNo);
                    }
                });
            } catch (Exception e) {
                System.out.println("空闲60秒下发缓存命令【失败】 " + deviceNo);
                e.printStackTrace();
            }
        }
    }
}
