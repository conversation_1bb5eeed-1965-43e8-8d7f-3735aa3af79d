/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package com.memory;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson2.JSONObject;
import com.memory.dto.*;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import redis.clients.jedis.Jedis;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;

import static com.memory.CommonUtil.reply;
import static com.memory.Redis.jedisPool;
import static com.memory.ServerUtil.*;
import static com.memory.Tdengine.tdengineds;
import static com.memory.dto.DeviceEnum.*;

@Slf4j
public class TcpSnoopServerHandler extends SimpleChannelInboundHandler {
    //public static volatile ChannelGroup channelGroup = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);
    public static volatile ConcurrentHashMap<String, ClientInfo> clients = new ConcurrentHashMap<>();
    TimeZone cnTimeZone = TimeZone.getTimeZone("GMT+8:00");
    public static String pushUrl = "";


    public TcpSnoopServerHandler(String pushUrl) {
        this.pushUrl = pushUrl;
    }

    int count = 0;

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object obj) throws Exception {
        //
        if (obj instanceof IdleStateEvent event) {
            if (IdleState.READER_IDLE.equals(event.state())) {
                count++;
                if (null != ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get()) {
                    String deviceNo = ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get().toString();
                    log.info("心跳 " + deviceNo + " " + count);
                }
                if (count > 4) {
                    if (null != ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get()) {
                        String deviceNo = ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get().toString();
                        log.info("心跳机制关闭链接 " + deviceNo);
                        String offlineTime = new DateTime().setTimeZone(cnTimeZone).toString();
                        HashMap<String, Object> tdata = new HashMap<>();
                        tdata.put("deviceNo", deviceNo);
                        tdata.put("type", 9);
                        tdata.put("offlineTime", offlineTime);
                        transfer(tdata);
                    } else log.info("心跳机制关闭链接 " + ctx.channel().toString());
                    closeChannel(ctx.channel(), ctx, "userEventTriggered");
                } else {
                    if (null != ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get()) {
                        String deviceNo = ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get().toString();
                        cacheCmdSend(deviceNo);
                    } else log.info("空闲60秒触发 " + ctx.channel().toString());
                }


            }
        } else {
            super.userEventTriggered(ctx, obj);
        }
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
        Channel channel = ctx.channel();
        if (null != channel.attr(AttributeKey.valueOf("deviceNo")).get()) {
            String deviceNo = channel.attr(AttributeKey.valueOf("deviceNo")).get().toString();
            log.info("链接断开 " + deviceNo);
            String offlineTime = new DateTime().setTimeZone(cnTimeZone).toString();
            HashMap<String, Object> tdata = new HashMap<>();
            tdata.put("deviceNo", deviceNo);
            tdata.put("type", 4);
            tdata.put("offlineTime", offlineTime);
            transfer(tdata);
        } else {
            log.info("链接断开 " + channel.toString());
        }
        closeChannel(channel, ctx, "handlerRemoved");
    }

    @Override
    public void channelRead0(ChannelHandlerContext ctx, Object msg) {

        String payload = msg.toString();
        log.info("channelRead0 " + payload);
        long unixtime = System.currentTimeMillis() / 1000;
        // 设备上线
        if (payload.contains("*") && payload.contains("#")) {
            System.out.println(ctx.channel().id().asLongText());
            payload = payload.replaceAll("\\*", "");
            String[] payloads = payload.split("#");
            String productCode = payloads[0];
            String deviceNo = CommonUtil.reverse(payloads[1]);
            String script = CommonUtil.reverse(payloads[2]);
            ClientInfo client = clients.get(deviceNo);
            log.info("deviceNo " + deviceNo);
            try (Jedis redis = jedisPool.getResource()) {
                String value = redis.hget("cache:" + deviceNo, "code");
                if (!redis.hexists("cache:" + deviceNo, "code")) {
                    closeChannel(ctx.channel(), ctx, "ini1");
                    log.info(deviceNo + " 认证失败 1");
                    return;
                }
                if (!productCode.equals(redis.hget("cache:" + deviceNo, "code").replaceAll("\"", ""))) {
                    closeChannel(ctx.channel(), ctx, "ini2");
                    log.info(deviceNo + " 认证失败 2");
                    return;
                } else log.info(deviceNo + " 认证通过");
            }
            if (null != client) {
                client.setStatus(0);
                closeChannel(client.getChannel(), null, "ini3");
            }
            DateTime dt = new DateTime().setTimeZone(cnTimeZone);
            ctx.channel().attr(AttributeKey.valueOf("deviceNo")).set(deviceNo);
            ClientInfo clientInfo = new ClientInfo();
            clientInfo.setCode(productCode);
            clientInfo.setChannel(ctx.channel());
            clientInfo.setStatus(1);
            clientInfo.setDeviceNo(deviceNo);
            clientInfo.setHeartTime(dt);
            clientInfo.setOnlineTime(dt);
            clientInfo.setOfflineTime(null);
            clientInfo.setUnixtime(unixtime);
            clients.put(deviceNo, clientInfo);
            HashMap<String, Object> tdata = new HashMap<>();
            tdata.put("deviceNo", deviceNo);
            tdata.put("type", 3);
            tdata.put("onlineTime", dt.toString());
            transfer(tdata);
        } else {
            // todo本地测试时 ，下面的if 注释掉
            if (null == ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get()) {
                closeChannel(ctx.channel(), null, "read01");
                return;
            }
            String deviceNo = ctx.channel().attr(AttributeKey.valueOf("deviceNo")).get().toString();
            count = 0;
            log.info("重置心跳 " + deviceNo + " " + count);
            ClientInfo client = clients.get(deviceNo);
            if (null == client || client.getStatus() != 1) {
                log.info("设备{}未上线, 关闭链接 " , deviceNo);
                closeChannel(ctx.channel(), null, "read02");
                clients.remove(deviceNo);
                return;
            }
            client.setHeartTime(new DateTime().setTimeZone(cnTimeZone));
            client.setUnixtime(unixtime);
            //
            try {
                HashMap<String, Object> tdata = new HashMap<>();
                tdata.put("deviceNo", deviceNo);
                tdata.put("data", payload);
                tdata.put("type", 0);
                tdata.put("heartTime", client.getHeartTime().toString());
                tdata.put("unixtime", unixtime);
                transfer(tdata);
            } catch (Exception ex) {
                log.error(ex.getMessage() + " " + ex);
            }
            String value = null;
            //获取阀门设备ID
            try(Jedis redis = jedisPool.getResource()){
                value = redis.hget("cache:" + deviceNo,"deviceTypeId");
                if (value != null) value = value.replaceAll("\"","");

            }
            try {
                // 气象站编号6024000011~6024000015，五台

                // 杀虫灯编号7024000011~7024000020，十台
                //List<String> list = Arrays.asList("7024000011", "7024000012", "7024000013", "7024000014", "7024000015", "7024000016", "7024000017", "7024000018", "7024000019", "7024000020");
                    // 杀虫灯数据处理
                if(payload!= null && !payload.isEmpty()){
                    int index = 14;
                    //控制码
                    String controlCode = payload.substring(index, index + 2);
                    index = index+6;
                    //协议标识（小端数转成大端数）
                    String agreement = CommonUtil.reverse(payload.substring(index, index + 4));
                    if (controlCode.equals("81") && agreement.equals("9070")){
                        InsectKillerDataDTO insectKillerDataDTO = DataParser.parse(payload);
                        if (null != insectKillerDataDTO) {
                            try {
                                //HashMap<String, Object> result = CommonUtil.parseSensorData(payload);
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(insectKillerDataDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(insectKillerDataDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);

                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                        return;
                    }
                }
                if (payload!= null && !payload.isEmpty()){
                    int index = 14;
                    //控制码
                    String controlCode = payload.substring(index, index + 2);
                    index = index+6;
                    //协议标识（小端数转成大端数）
                    String agreement = CommonUtil.reverse(payload.substring(index, index + 4));
                    //杀虫灯读工作模式
                    if (controlCode.equals("83") && agreement.equals("8070")){
                        InsectKillReadWorkDTO insectKillReadWorkDTO = DataParser.inseckKillReadWorkParse(payload);
                        if (null != insectKillReadWorkDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(insectKillReadWorkDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(insectKillReadWorkDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //杀虫灯写工作模式
                    if (controlCode.equals("84") && agreement.equals("8070")){
                        InsectKillWriteWorkDTO insectKillWriteWorkDTO = DataParser.inseckKillWriteWorkParse(payload);
                        if (null != insectKillWriteWorkDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(insectKillWriteWorkDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(insectKillWriteWorkDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                }
                //判断设备是否是智能阀控器
                //开始判断是读取阀门控制还是设置阀门控制数据
                if(payload!= null && !payload.isEmpty()){
                    int index = 14;
                    //控制码
                    String controlCode = payload.substring(index, index + 2);
                    index = index+6;
                    //协议标识（小端数转成大端数）
                    String agreement = CommonUtil.reverse(payload.substring(index, index + 4));
                    //读阀门数据
                    if((controlCode.equals("81")|| controlCode.equals("85")) && agreement.equals("9086")){
                        ReadValveDateDto readValveDateDto = DataParser.readValveDateParse(payload);
                        if (null != readValveDateDto) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(readValveDateDto) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(readValveDateDto));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //重置阀门运行时间
                    if(controlCode.equals("82") && agreement.equals("AABB")){
                        ResetValveDateDto resetValveDateDto = DataParser.resetValveDateParse(payload);
                        if (null != resetValveDateDto) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(resetValveDateDto) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(resetValveDateDto));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //读取阀门开机自检
                    if(controlCode.equals("83") && agreement.equals("801A")){
                        ReadValveOpenOnSelfTestDto readValveOpenOnSelfTestDto = DataParser.readValveOpenOnSelfTestParse(payload);
                        if (null != readValveOpenOnSelfTestDto) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(readValveOpenOnSelfTestDto) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(readValveOpenOnSelfTestDto));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //设置阀门开机自检
                    if(controlCode.equals("84") && agreement.equals("801A")){
                        SetUpValveOpenOnSelfTestDTO setUpValveOpenOnSelfTestDTO = DataParser.setUpValveOpenOnSelfTestParse(payload);
                        if (null != setUpValveOpenOnSelfTestDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(setUpValveOpenOnSelfTestDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(setUpValveOpenOnSelfTestDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //读取阀门异常控制使能
                    if(controlCode.equals("83") && agreement.equals("801B")){
                        ReadValveAbnormalControlDTO readValveAbnormalControlDTO = DataParser.readValveAbnormalControlParse(payload);
                        if (null != readValveAbnormalControlDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(readValveAbnormalControlDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(readValveAbnormalControlDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //设置阀门异常控制使能
                    if(controlCode.equals("84") && agreement.equals("801B")){
                        SetUpValveAbnormalControlDTO setUpValveAbnormalControlDTO = DataParser.setUpValveAbnormalControlParse(payload);
                        if (null != setUpValveAbnormalControlDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(setUpValveAbnormalControlDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(setUpValveAbnormalControlDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //读取阀门控制模式
                    if (controlCode.equals("83") && agreement.equals("801C")){

                        ReadValveControlModeDataDTO readDataDTO = DataParser.readValveParse(payload);
                        if (null != readDataDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(readDataDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(readDataDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //设置阀门控制模式
                    if (controlCode.equals("84") && agreement.equals("801C")){
                        //
                        SetUpValveControlModeDataDTO setUpDataDTO = DataParser.setUpValveParse(payload);
                        if (null != setUpDataDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(setUpDataDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(setUpDataDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //读取阀门上下参数
                    if (controlCode.equals("83") && agreement.equals("801D")){
                        //
                        ReadValveUpAndDownParamterDTO readValveUpAndDownParamterDTO = DataParser.readValveUpAndDownParamterParse(payload);
                        if (null != readValveUpAndDownParamterDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(readValveUpAndDownParamterDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(readValveUpAndDownParamterDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //设置阀门上下参数
                    if (controlCode.equals("84") && agreement.equals("801D")){
                        //
                        SetUpValveUpAndDownParamterDTO setUpValveUpAndDownParamterDTO = DataParser.setUpValveUpAndDownParamterParse(payload);
                        if (null != setUpValveUpAndDownParamterDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(setUpValveUpAndDownParamterDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(setUpValveUpAndDownParamterDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                }/*else {
                    return;
                }*/
                //开始判断是智能采集控制器
                if(payload!= null && !payload.isEmpty()){
                    int index = 14;
                    //控制码
                    String controlCode = payload.substring(index, index + 2);
                    index = index+6;
                    //协议标识（小端数转成大端数）
                    String agreement = CommonUtil.reverse(payload.substring(index, index + 4));
                    //读温室传感器数据
                    if((controlCode.equals("81")||controlCode.equals("85"))  && agreement.equals("9003")){
                        ReadGreenhouseSensorsDTO readGreenhouseSensorsDTO = DataParser.readGreenhouseSensorsParse(payload);
                        log.info("{},{},{}",deviceNo,controlCode,agreement);
                        if (null != readGreenhouseSensorsDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(readGreenhouseSensorsDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(readGreenhouseSensorsDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                }
                //开始判断是否为模拟量监测
                if (payload!= null && !payload.isEmpty()){
                    //
                    int index = 14;
                    //控制码
                    String controlCode = payload.substring(index, index + 2);
                    index = index+6;
                    //协议标识（小端数转成大端数）
                    String agreement = CommonUtil.reverse(payload.substring(index, index + 4));
                    //判断是否为模拟量监测
                    if(controlCode.equals("85") && agreement.equals("9053")){
                        ReadAnalogMonitorDTO readAnalogMonitorDTO = DataParser.readAnalogMonitorParse(payload);
                        System.out.println(readAnalogMonitorDTO);
                        if (null != readAnalogMonitorDTO) {
                            try {
                                try (Connection conn = tdengineds.getConnection()) {
                                    Statement stmt = conn.createStatement();
                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(readAnalogMonitorDTO) + "')");
                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(readAnalogMonitorDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                    //读模拟量通道参数
                    if (controlCode.equals("83") && agreement.equals("800F")){
                        RedPassageParameterDTO redPassageParameterDTO = DataParser.readPassageParameter(payload);
                        System.out.println(redPassageParameterDTO);
                        if (null != redPassageParameterDTO) {
                            try {
//                                try (Connection conn = tdengineds.getConnection()) {
//                                    Statement stmt = conn.createStatement();
//                                    stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(redPassageParameterDTO) + "')");
//                                }
                                HashMap<String, Object> tdata = new HashMap<>();
                                if (redPassageParameterDTO.getPassage()!=null){
                                    Integer passage = redPassageParameterDTO.getPassage();
                                    switch (passage){
                                        case 0: tdata.put("passage",1); break;
                                        case 1: tdata.put("passage",2); break;
                                        case 2: tdata.put("passage",3); break;
                                        case 3: tdata.put("passage",4); break;
                                    }
                                }
                                tdata.put("deviceNo", deviceNo);
                                tdata.put("data", JSONObject.toJSONString(redPassageParameterDTO));
                                tdata.put("type", 1);
                                tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                                tdata.put("unixtime", unixtime);
                                transfer2(tdata);
                            }catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    }
                }
                HashMap<String, Object> hm = CommonUtil.divideHeader(payload);
                if (null == hm) return;
                String ser = hm.get("ser").toString();
                deviceNo = hm.get("no").toString();
                //
                // System.out.println("client.getDeviceNo() " + client.getDeviceNo());
                // System.out.println(clients.toString());
                String controlCode = hm.get("control-code").toString();
                String functionCode = hm.get("function-code").toString();
                //
                // System.out.println("ser " + ser);
                log.info("deviceNo:" + deviceNo + " controlCode:" + controlCode +
                        " functionCode:" + functionCode + " ser:" + ser + " payload:" + payload);
                // System.out.println("controlCode " + controlCode);
                // System.out.println("functionCode " + functionCode);
                //85:主动上报，81：主动读取
                if (controlCode.equals("85") || controlCode.equals("81")) {
                    String reply = reply(payload);
                    System.out.println(reply);
                    sendCmd(deviceNo, reply);
                }
                switch (functionCode) {
                    case "9005":
                        try {
                            // 前纬度  后经度
                            String latitude = "0";
                            String longitude = "0";
                            if (payload.length() > 16 * 2 + 15 * 2 + 15 * 2) {
                                latitude = CommonUtil.convertHexToAscii(payload.substring(16 * 2, 16 * 2 + 15 * 2));
                                longitude = CommonUtil.convertHexToAscii(payload.substring(16 * 2 + 15 * 2, 16 * 2 + 15 * 2 + 15 * 2));
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        // 刷新 快照实时状态
                        break;
                    // 实时 历史
                    case "9002":
                    case "9013":
                        // 非心跳
                        // 刷新 快照实时状态
                        try {
                             HashMap<String, Object> result = CommonUtil.parseSensorData(payload);
                            try (Connection conn = tdengineds.getConnection()) {
                                Statement stmt = conn.createStatement();
                                stmt.execute("INSERT INTO sensor_" + deviceNo + " USING sensor TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(result) + "')");
                            }
                            HashMap<String, Object> tdata = new HashMap<>();
                            tdata.put("deviceNo", deviceNo);
                            tdata.put("data", JSONObject.toJSONString(result));
                            tdata.put("type", 1);
                            tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                            tdata.put("unixtime", unixtime);
                            transfer(tdata);

                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        break;
                    case "9001":
                    case "900B":
                        try {
                            HashMap<String, Object> result = CommonUtil.parseControlData(payload);
                            try (Connection conn = tdengineds.getConnection()) {
                                Statement stmt = conn.createStatement();
                                stmt.execute("INSERT INTO control_" + deviceNo + " USING control TAGS(" + deviceNo + ") VALUES (NOW,'" + deviceNo + "','" + JSONObject.toJSONString(result) + "')");
                            }
                            HashMap<String, Object> tdata = new HashMap<>();
                            tdata.put("deviceNo", deviceNo);
                            tdata.put("data", JSONObject.toJSONString(result));
                            tdata.put("type", 1);
                            tdata.put("mirror", JSONObject.toJSONString(result));
                            tdata.put("hearTime", new DateTime().setTimeZone(cnTimeZone).toString());
                            tdata.put("unixtime", unixtime);
                            transfer(tdata);

                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                }
            } catch (Exception ex) {

            }
        }
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        Channel channel = ctx.channel();

        if (null != cause) {
            log.error(cause.toString());
            if (null != cause.getCause()) {
                log.error(cause.getCause().toString());
            }
            if (null != cause.getMessage()) {
                log.error(cause.getMessage().toString());
            }
        }
        if (null != channel.attr(AttributeKey.valueOf("deviceNo")).get()) {
            String deviceNo = channel.attr(AttributeKey.valueOf("deviceNo")).get().toString();
            log.error("链接异常 " + deviceNo);
            HashMap<String, Object> tdata = new HashMap<>();
            tdata.put("deviceNo", deviceNo);
            tdata.put("type", 4);
            tdata.put("offlineTime", new DateTime().setTimeZone(cnTimeZone).toString());
            transfer(tdata);
        } else {
            log.error("链接异常断开 " + channel.toString());
        }
        closeChannel(channel, ctx, "exceptionCaught");
    }

    public static void closeChannel(Channel channel, ChannelHandlerContext ctx, String message) {
        if (null == channel) return;
        try {
            log.info("closeChannel " + message);
            if (null != channel.attr(AttributeKey.valueOf("deviceNo")).get() && !message.equals("handlerRemoved")) {
                String deviceNo = channel.attr(AttributeKey.valueOf("deviceNo")).get().toString();

                if (!clients.get(deviceNo).getChannel().isActive()) {
                    clients.remove(deviceNo);
                }
            }
            channel.close();
            if (null != ctx) {
                ctx.close();
                ctx.channel().close();
            }
            channel = null;
        } catch (Exception ex) {

        }
    }

   public static void main(String[] args) {

       String payload = "68020000246068855F0003901E270D1507193F414D00000000000807382F2505270179000098020200000000000000000104230026002A0800000200380A00000300161D000008003C0200000204230026002A08000027000000000002005B0A00000300FF160000016616";
       int index = 14;
       //控制码
       String controlCode = payload.substring(index, index + 2);
       index = index+6;
       //协议标识（小端数转成大端数）
       String agreement = CommonUtil.reverse(payload.substring(index, index + 4));
       System.out.println(controlCode);
       System.out.println(agreement);
       ReadGreenhouseSensorsDTO readGreenhouseSensorsDTO = DataParser.readGreenhouseSensorsParse(payload);
       System.out.println(JSONObject.toJSONString(readGreenhouseSensorsDTO));
       String reply = reply(payload);
       System.out.println(reply);

   }
}
