package com.memory;

import org.redisson.RedissonRedLock;
import org.redisson.api.RLock;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.Vector;

public class test {
    public static void main(String[] args) {
       /* System.out.println(BigDecimal.valueOf(0).compareTo(BigDecimal.ZERO));
        System.out.println(new Random().nextInt(1000));*/
        System.out.println(CommonUtil.parseSensorData("6867000023806885890002902130110804191F3315000000000008070000240422010C0D0F00BF5401000000000086000000000000000000000000000000000000000000000078EC0000E600B986000000000000000000000000B806DD04000000005D06AE04000000002B06760900000000400600000000000000000000000000000000000000000000000000000000000001B816"));
    }
}
