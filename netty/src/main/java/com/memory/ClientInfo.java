package com.memory;

import cn.hutool.core.date.DateTime;
import io.netty.channel.Channel;
import io.netty.channel.ChannelPipeline;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.net.SocketAddress;
import java.util.Date;

@Data
public class ClientInfo {
    private String code;
    private String deviceNo;
    private Channel channel;
    private int status = 0;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private DateTime onlineTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private DateTime offlineTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private DateTime heartTime;

    private Long unixtime;

    private SocketAddress remote;

    private SocketAddress local;

    private ChannelPipeline pipeline;

    // 1 普通模式  2透传模式
    private Integer model = 1;
}
