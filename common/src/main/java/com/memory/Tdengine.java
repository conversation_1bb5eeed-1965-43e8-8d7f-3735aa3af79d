package com.memory;

import com.taosdata.jdbc.TSDBDriver;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;
@Component
public class Tdengine {
    @Value("${tdengine.url}")
    String url;

    @Value("${tdengine.username}")
    String username;

    @Value("${tdengine.password}")
    String password;
    static HikariConfig config = new HikariConfig();
    static HikariDataSource tdengineds = null;

    @PostConstruct
    void TdengineIni() {
        config.setDriverClassName("com.taosdata.jdbc.TSDBDriver");
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        // connection pool configurations
        config.setMinimumIdle(2);          //minimum number of idle connection
        config.setMaximumPoolSize(10);      //maximum number of connection in the pool
        config.setConnectionTimeout(30000); //maximum wait milliseconds for get connection from pool
        config.setMaxLifetime(0);           // maximum life time for each connection
        config.setIdleTimeout(0);           // max idle time for recycle idle connection
        config.setConnectionTestQuery("select server_status()"); //validation query
        tdengineds = new HikariDataSource(config); //create datasource

    }
}
