package com.memory;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.taosdata.jdbc.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;


import java.util.Arrays;
import java.util.HashMap;

@Slf4j
public class HttpTransfer {
    public static void post(String urls, HashMap<String, Object> paramMap) {
        log.info("开始发送HTTP请求，URL: {}, 参数: {}", urls, JSON.toJSONString(paramMap));
        Arrays.stream(urls.split("\\\\n")).parallel().forEach(url -> {
            String u = url.replaceAll("\"", "").trim();
            if (StringUtils.isEmpty(u)) return;
            HttpRequest.post(u)
                    .header("Content-Type", "application/json")
                    .timeout(5000)
                    .body(JSONObject.toJSONString(paramMap)).execute().close();
        });
    }

}
