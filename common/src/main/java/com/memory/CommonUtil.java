package com.memory;

import cn.hutool.core.util.StrUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Base64;

@Slf4j
public class CommonUtil {


    private static MessageDigest mdInst;

    {

        try {
            mdInst = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    public static String getLength(String str) {
        return CommonUtil.reverse("0000" + Integer.toHexString(str.length() / 2)).substring(0, 4).toUpperCase();
    }

    public static String reverse(String str) {
        if (str.length() % 2 != 0) str = "0" + str;
        char[] a = str.toCharArray();
        StringBuilder b = new StringBuilder();
        for (int i = a.length; i > 0; i -= 2) {
            b.append(a[i - 2]);
            b.append(a[i - 1]);
        }
        return b.toString();
    }

    public static String convertStringToHex(String str) {
        char[] chars = str.toCharArray();
        StringBuffer hex = new StringBuffer();
        for (int i = 0; i < chars.length; i++) {
            hex.append(Integer.toHexString((int) chars[i]));
        }
        return hex.toString().toUpperCase();
    }

    public static String convertHexToAscii(String hexString) {
        hexString = hexString.replaceAll("0", "");
        StringBuilder output = new StringBuilder();
        for (int i = 0; i < hexString.length(); i += 2) {
            String str = hexString.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }
        return output.toString();
    }

    public static String sum(String str) {
        char[] a = str.replaceAll("\\s*", "").toCharArray();
        Integer sum = 0;
        for (int i = a.length; i > 0; i -= 2) {
            StringBuilder b = new StringBuilder();
            b.append(a[i - 2]);
            b.append(a[i - 1]);
            sum += Integer.parseInt(b.toString(), 16);
        }
        String r = Integer.toHexString(sum).toUpperCase();
        return r.substring(r.length() - 2).toString();
    }

    public static HashMap<String, Object> divideHeader(String str) {
        HashMap<String, Object> hm = new HashMap<>();
        try {
            str = str.replaceAll("\\s*", "");

            if (str.length() <= 10) return null;
            int index = 0;
            index += 2;
            hm.put("no", CommonUtil.reverse(str.substring(index, index += 2 * 5)));
            index += 2;
            hm.put("control-code", str.substring(index, index += 2));
            hm.put("length", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
            String function_code = CommonUtil.reverse(str.substring(index, index += 2 * 2));
            hm.put("function-code", function_code);
            hm.put("ser", str.substring(str.length() - 6, str.length() - 4));
        } catch (Exception ex) {
            hm = null;
        }
        return hm;
    }


    public static String reply(String value) {
        try {
            HashMap<String, Object> hm = CommonUtil.divideHeader(value);
            String no = hm.get("no").toString();
            String controlCode = hm.get("control-code").toString();
            String functionCode = hm.get("function-code").toString();
            StringBuilder text = new StringBuilder();
            LocalDateTime localDateTime = LocalDateTime.now();
            text.append(value.substring(0, 14));
            LocalDateTime time = LocalDateTime.now();
            switch (functionCode) {
                case "9001":
                    text.append("05 0900 0190");
                    String scond = Integer.toHexString(localDateTime.getSecond());
                    String minute = Integer.toHexString(localDateTime.getMinute());
                    String hour = Integer.toHexString(localDateTime.getHour());
                    String day = Integer.toHexString(localDateTime.getDayOfMonth());
                    String month = Integer.toHexString(localDateTime.getMonthValue());
                    String year = Integer.toHexString(Integer.parseInt(String.valueOf(localDateTime.getYear()).substring(2)));
                    text.append(scond.length() < 2 ? ("0" + scond) : scond);
                    text.append(minute.length() < 2 ? ("0" + minute) : minute);
                    text.append(hour.length() < 2 ? ("0" + hour) : hour);
                    text.append(day.length() < 2 ? ("0" + day) : day);
                    text.append(month.length() < 2 ? ("0" + month) : month);
                    text.append(year.length() < 2 ? ("0" + year) : year);
                    text.append(value.substring(290, 292));//SER
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
                    break;
                case "9002":
                    text.append("05");
                    // 秒 分 时 日 月 年
                    StringBuilder text9002 = new StringBuilder();
                    text9002.append("0290");
                    text9002.append(CommonUtil.reverse("00" + Integer.toHexString(time.getSecond())).substring(0, 2));
                    text9002.append(CommonUtil.reverse("00" + Integer.toHexString(time.getMinute())).substring(0, 2));
                    text9002.append(CommonUtil.reverse("00" + Integer.toHexString(time.getHour())).substring(0, 2));
                    text9002.append(CommonUtil.reverse("00" + Integer.toHexString(time.getDayOfMonth())).substring(0, 2));
                    text9002.append(CommonUtil.reverse("00" + Integer.toHexString(time.getMonthValue())).substring(0, 2));
                    text9002.append(CommonUtil.reverse("00" + Integer.toHexString(Integer.parseInt(String.valueOf(time.getYear()).substring(2)))).substring(0, 2));
                    text9002.append(value.substring(292, 294));//SER
                    text.append(CommonUtil.getLength(text9002.toString()));
                    text.append(text9002);
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
                    break;
                case "9003":
                    log.info("进入9003");
                    text.append("05");
                    // 秒 分 时 日 月 年
                    StringBuilder text9003 = new StringBuilder();
                    /*text9003.append("0390");
                    //秒
                    String second9003 = Integer.toHexString(localDateTime.getSecond());
                    System.out.println("秒：" + second9003);
                    //分
                    String minute9003  = Integer.toHexString(localDateTime.getMinute());
                    System.out.println("分：" + minute9003);
                    //时
                    String hour9003  = Integer.toHexString(localDateTime.getHour());
                    System.out.println("时：" + hour9003);
                    //日
                    String day9003  = Integer.toHexString(localDateTime.getDayOfMonth());
                    System.out.println("日：" + day9003);
                    //月
                    String month9003  = Integer.toHexString(localDateTime.getMonthValue());
                    System.out.println("月：" + month9003);
                    //年
                    String year9003= Integer.toHexString(Integer.parseInt(String.valueOf(localDateTime.getYear()).substring(2)));
                    System.out.println("年：" + year9003);
                    text9003.append(second9003.length() < 2 ? ("0" + second9003) : second9003);
                    text9003.append(minute9003.length() < 2 ? ("0" + minute9003) : minute9003);
                    text9003.append(hour9003.length() < 2 ? ("0" + hour9003) : hour9003);
                    text9003.append(day9003.length() < 2 ? ("0" + day9003) : day9003);
                    text9003.append(month9003.length() < 2 ? ("0" + month9003) : month9003);
                    text9003.append(year9003.length() < 2 ? ("0" + year9003) : year9003);
                    text9003.append(value.substring(value.length() - 6, value.length() - 4));//SER
                    text.append(CommonUtil.getLength(text9003.toString()));
                    text.append(text9003);*/
                    text9003.append("0390");
                    text9003.append(CommonUtil.reverse("00" + Integer.toHexString(time.getSecond())).substring(0, 2));
                    text9003.append(CommonUtil.reverse("00" + Integer.toHexString(time.getMinute())).substring(0, 2));
                    text9003.append(CommonUtil.reverse("00" + Integer.toHexString(time.getHour())).substring(0, 2));
                    text9003.append(CommonUtil.reverse("00" + Integer.toHexString(time.getDayOfMonth())).substring(0, 2));
                    text9003.append(CommonUtil.reverse("00" + Integer.toHexString(time.getMonthValue())).substring(0, 2));
                    text9003.append(CommonUtil.reverse("00" + Integer.toHexString(Integer.parseInt(String.valueOf(time.getYear()).substring(2)))).substring(0, 2));
                    text9003.append(value.substring(value.length() - 6, value.length() - 4));;//SER
                    text.append(CommonUtil.getLength(text9003.toString()));
                    text.append(text9003);
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
                    break;
                case "9005":
                    text.append("05 0300 0590");
                    text.append(value.substring(92, 94));//SER
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
                    break;
                case "900B":
                    text.append("05 0300 0B90");
                    text.append(value.substring(290, 292));//SER
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
                    break;
                case "9013":
                    text.append("05");
                    StringBuilder text9013 = new StringBuilder();
                    text9013.append("1390");
                    text9013.append(CommonUtil.reverse("00" + Integer.toHexString(time.getSecond())).substring(0, 2));
                    text9013.append(CommonUtil.reverse("00" + Integer.toHexString(time.getMinute())).substring(0, 2));
                    text9013.append(CommonUtil.reverse("00" + Integer.toHexString(time.getHour())).substring(0, 2));
                    text9013.append(CommonUtil.reverse("00" + Integer.toHexString(time.getDayOfMonth())).substring(0, 2));
                    text9013.append(CommonUtil.reverse("00" + Integer.toHexString(time.getMonthValue())).substring(0, 2));
                    text9013.append(CommonUtil.reverse("00" + Integer.toHexString(Integer.parseInt(String.valueOf(time.getYear()).substring(2)))).substring(0, 2));
                    text9013.append(value.substring(292, 294));//SER
                    text.append(CommonUtil.getLength(text9013.toString()));
                    text.append(text9013);
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
                    break;
                //返回杀虫灯数据
                case "9070":
                    //控制码 数据长度 协议标识
                    text.append("05 0900 7090");
                    //秒
                    String second9070 = Integer.toHexString(localDateTime.getSecond());
                    //分
                    String minute9070 = Integer.toHexString(localDateTime.getMinute());
                    //时
                    String hour9070 = Integer.toHexString(localDateTime.getHour());
                    //日
                    String day9070 = Integer.toHexString(localDateTime.getDayOfMonth());
                    //月
                    String month9070 = Integer.toHexString(localDateTime.getMonthValue());
                    //年
                    String year9070 = Integer.toHexString(Integer.parseInt(String.valueOf(localDateTime.getYear()).substring(2)));
                    text.append(second9070.length() < 2 ? ("0" + second9070) : second9070);
                    text.append(minute9070.length() < 2 ? ("0" + minute9070) : minute9070);
                    text.append(hour9070.length() < 2 ? ("0" + hour9070) : hour9070);
                    text.append(day9070.length() < 2 ? ("0" + day9070) : day9070);
                    text.append(month9070.length() < 2 ? ("0" + month9070) : month9070);
                    text.append(year9070.length() < 2 ? ("0" + year9070) : year9070);
                    text.append(value.substring(value.length() - 6, value.length() - 4));//SER
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
                    break;
                //返回模拟量采集控制
                case "9053":
                    //控制码 数据长度 协议标识
                    text.append("05 0900 5390");
                    //秒
                    String second9053 = Integer.toHexString(localDateTime.getSecond());
                    //分
                    String minute9053 = Integer.toHexString(localDateTime.getMinute());
                    //时
                    String hour9053 = Integer.toHexString(localDateTime.getHour());
                    //日
                    String day9053 = Integer.toHexString(localDateTime.getDayOfMonth());
                    //月
                    String month9053 = Integer.toHexString(localDateTime.getMonthValue());
                    //
                    String year9053 = Integer.toHexString(Integer.parseInt(String.valueOf(localDateTime.getYear()).substring(2)));
                    text.append(second9053.length() < 2 ? ("0" + second9053) : second9053);
                    text.append(minute9053.length() < 2 ? ("0" + minute9053) : minute9053);
                    text.append(hour9053.length() < 2 ? ("0" + hour9053) : hour9053);
                    text.append(day9053.length() < 2 ? ("0" + day9053) : day9053);
                    text.append(month9053.length() < 2 ? ("0" + month9053) : month9053);
                    text.append(year9053.length() < 2 ? ("0" + year9053) : year9053);
                    text.append(value.substring(value.length() - 6, value.length() - 4));//SER
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
                    break;
                    //控制阀
                case "9086":
                    //控制码 数据长度 协议标识
                    text.append("05 0900 8690");
                    //秒
                    String second9086 = Integer.toHexString(localDateTime.getSecond());
                    //分
                    String minute9086 = Integer.toHexString(localDateTime.getMinute());
                    //时
                    String hour9086 = Integer.toHexString(localDateTime.getHour());
                    //日
                    String day9086 = Integer.toHexString(localDateTime.getDayOfMonth());
                    //月
                    String month9086 = Integer.toHexString(localDateTime.getMonthValue());
                    //年
                    String year9086= Integer.toHexString(Integer.parseInt(String.valueOf(localDateTime.getYear()).substring(2)));
                    text.append(second9086.length() < 2 ? ("0" + second9086) : second9086);
                    text.append(minute9086.length() < 2 ? ("0" + minute9086) : minute9086);
                    text.append(hour9086.length() < 2 ? ("0" + hour9086) : hour9086);
                    text.append(day9086.length() < 2 ? ("0" + day9086) : day9086);
                    text.append(month9086.length() < 2 ? ("0" + month9086) : month9086);
                    text.append(year9086.length() < 2 ? ("0" + year9086) : year9086);
                    text.append(value.substring(value.length() - 6, value.length() - 4));//SER
                    text.append(CommonUtil.sum(text.toString()));
                    text.append(16);
            }
            return text.toString().replaceAll("\\s*", "");
        } catch (Exception ex) {

            log.info("数据解析错误 " + value + " " + ex.getMessage() + " " + ex);
            return "";
        }
    }

//    public static void main(String[] args) {
//        String data = "6897000022806885890002900C36070B03187F33188B0300040008070000240130000B0C0F00010001000100010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000979916";
//        parseSensorData(data);
//    }

    // 解析采集器
    public static HashMap<String, Object> parseSensorData(String str) {
        HashMap<String, Object> hm = new HashMap<>();
        try {
            int index = 0;
            index += 2;
            hm.put("no", CommonUtil.reverse(str.substring(index, index += 2 * 5)));
            index += 2;
            hm.put("control-code", str.substring(index, index += 2));
            hm.put("length", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
            String function_code = CommonUtil.reverse(str.substring(index, index += 2 * 2));
            hm.put("function-code", function_code);
            log.info(LocalDateTime.now() + " " + hm.toString() + " " + str);
            String time = CommonUtil.reverse(str.substring(index, index += 2 * 6));
            LocalDate localDate = LocalDate.now();
            int year = Integer.parseInt(time.substring(0, 2), 16);
            int month = Integer.parseInt(time.substring(2, 4), 16);
            int day = Integer.parseInt(time.substring(4, 6), 16);
            int hour = Integer.parseInt(time.substring(6, 8), 16);
            int minute = Integer.parseInt(time.substring(8, 10), 16);
            int second = Integer.parseInt(time.substring(10, 12), 16);
            if (year <= 0 || month <= 0 || day <= 0) {
                throw new Exception("时间格式错误");
            }
            hm.put("time", String.valueOf(localDate.getYear()).substring(0, 2) + (year < 10 ? ("0" + year) : year) + "-" +
                    (month < 10 ? ("0" + month) : month) + "-" +
                    (day < 10 ? ("0" + day) : day)
                    + " " + (hour < 10 ? ("0" + hour) : hour) + ":" +
                    (minute < 10 ? ("0" + minute) : minute) + ":" +
                    (second < 10 ? ("0" + second) : second));
            hm.put("network-status", Integer.parseInt(str.substring(index, index += 2), 16));
            hm.put("signal-strength", Integer.parseInt(str.substring(index, index += 2), 16));
            hm.put("online-times", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16));
            Integer runningStatus = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("running-status", runningStatus);
            StringBuilder runningStatusInfo = new StringBuilder();
            //
            if ((runningStatus & (int) Math.pow(2, 0)) != 0) {
                runningStatusInfo.append("网络异常,");
            }
            if ((runningStatus & (int) Math.pow(2, 1)) != 0) {
                runningStatusInfo.append("电表异常,");
            }
            if ((runningStatus & (int) Math.pow(2, 2)) != 0) {
                runningStatusInfo.append("水表异常,");
            }
            if ((runningStatus & (int) Math.pow(2, 3)) != 0) {
                runningStatusInfo.append("FLASH,");
            }
            if ((runningStatus & (int) Math.pow(2, 4)) != 0) {
                runningStatusInfo.append("EEPROM,");
            }
            if ((runningStatus & (int) Math.pow(2, 5)) != 0) {
                runningStatusInfo.append("RFID,");
            }
            if ((runningStatus & (int) Math.pow(2, 6)) != 0) {
                runningStatusInfo.append("WWDGT,");
            }
            if ((runningStatus & (int) Math.pow(2, 7)) != 0) {
                runningStatusInfo.append("欠压,");
            }
            if ((runningStatus & (int) Math.pow(2, 8)) != 0) {
                runningStatusInfo.append("位移,");
            }
            if ((runningStatus & (int) Math.pow(2, 9)) != 0) {
                runningStatusInfo.append("GPS,");
            }
            hm.put("running-status-info", StrUtil.isNotEmpty(runningStatusInfo) ? runningStatusInfo.deleteCharAt(runningStatusInfo.length() - 1).toString() : "");
            //
            hm.put("upload-interval", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
            hm.put("power-voltage", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.001);
            hm.put("software-version", str.substring(index, index += 2 * 4));
            hm.put("hardware-version", CommonUtil.reverse(str.substring(index, index += 2 * 4)));
            //
            String sensorEnabledStatusHex = CommonUtil.reverse(str.substring(index, index += 2 * 4));
            hm.put("sensor-enabled-status", sensorEnabledStatusHex);
            int sensorEnabledStatus = Integer.parseInt(sensorEnabledStatusHex, 16);
            //
            String sensorCommunicationStatusHex = CommonUtil.reverse(str.substring(index, index += 2 * 4));
            hm.put("sensor-communication-status", sensorCommunicationStatusHex);
            int sensorCommunicationStatus = Integer.parseInt(sensorCommunicationStatusHex, 16);
            //
            String sensorFailureStatusHex = CommonUtil.reverse(str.substring(index, index += 2 * 4));
            hm.put("sensor-failure-status", sensorFailureStatusHex);
            int sensorFailureStatus = Integer.parseInt(sensorFailureStatusHex, 16);
            // 传感器预警1
            String sensorWarnStatusHex = CommonUtil.reverse(str.substring(index, index += 2 * 4));
            hm.put("sensor-warn-status", sensorWarnStatusHex);
            int sensorWarnStatus = Integer.parseInt(sensorWarnStatusHex, 16);
            // 传感器预警2
            index += 2 * 4;
            //设备类型(01：气象，02：土壤，03：设备+土壤)
            Integer devType = Integer.parseInt(str.substring(index, index += 2),16);
            hm.put("devType",devType);
            // 预留8字节
            index += 2 * 7;
            //
            StringBuilder communication = new StringBuilder();
            StringBuilder failure = new StringBuilder();
            StringBuilder warn = new StringBuilder();
            HashMap<String, Integer> useMap = new HashMap<>();
            if ((sensorEnabledStatus & (int) Math.pow(2, 1)) != 0) {
                hm.put("illumination-intensity", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 1)) != 0) {
                    communication.append("光照传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 1)) != 0) {
                    failure.append("光照传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 0)) != 0) {
                    warn.append("光照强度预警,");
                    hm.put("warn-illumination-intensity", 1);
                } else hm.put("warn-illumination-intensity", 0);
                //
                useMap.put("illumination-intensity", 1);
                hm.put("use-illumination-intensity", 1);
            } else {
                index += 2 * 4;
                useMap.put("illumination-intensity", 0);
                hm.put("use-illumination-intensity", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 2)) != 0) {
                // Short at = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                // 1. 先将 16 进制字符串解析为 Integer
                int tempIntValue = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                // 2. 将 Integer 强制转换为 short，Java 会自动处理溢出和补码
                short at = (short) tempIntValue;
                hm.put("air-temperature", BigDecimal.valueOf(at * 0.01).setScale(2, RoundingMode.HALF_UP));
                hm.put("air-humidity", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01)
                        .setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 2)) != 0) {
                    communication.append("空气温湿度传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 2)) != 0) {
                    failure.append("空气温湿度传感器故障,");
                } else {
                    if ((sensorWarnStatus & (int) Math.pow(2, 1)) != 0) {
                        warn.append("空气温度预警,");
                        hm.put("warn-air-temperature", 1);
                    } else hm.put("warn-air-temperature", 0);
                    //
                    if ((sensorWarnStatus & (int) Math.pow(2, 2)) != 0) {
                        warn.append("空气湿度预警,");
                        hm.put("warn-air-humidity", 1);
                    }
                }
                useMap.put("air-temperature", 1);
                useMap.put("air-humidity", 1);
                hm.put("use-air-temperature", 1);
                hm.put("use-air-humidity", 1);
            } else {
                index += 2 * 2;
                index += 2 * 2;
                useMap.put("air-temperature", 0);
                useMap.put("air-humidity", 0);
                hm.put("use-air-temperature", 0);
                hm.put("use-air-humidity", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 3)) != 0) {
                hm.put("wind-speed", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01)
                        .setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 3)) != 0) {
                    communication.append("风速传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 3)) != 0) {
                    failure.append("风速传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 3)) != 0) {
                    warn.append("风速预警,");
                    hm.put("warn-wind-speed", 1);
                } else hm.put("warn-wind-speed", 0);
                useMap.put("wind-speed", 1);
                hm.put("use-wind-speed", 1);
            } else {
                index += 2 * 2;
                useMap.put("wind-speed", 0);
                hm.put("use-wind-speed", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 4)) != 0) {
                //1北、2北东北、3东北、4东东北、5东、6东东南、7东南、8南东南、9南、10南西南、11西南、12西西南、13西、14西西北、15西北、16北西北、17无风向
                int direction = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                String strDirection = computeWindDirection(direction);
                if (Double.parseDouble(hm.get("wind-speed").toString()) > 0) {
                    //35999
                    hm.put("wind-direction-angle", Double.valueOf(direction / 100.00));
                    hm.put("wind-direction", strDirection);
                } else {
                    hm.put("wind-direction-angle", 0);
                    hm.put("wind-direction", "");
                }
                if ((sensorCommunicationStatus & (int) Math.pow(2, 4)) != 0) {
                    communication.append("风向传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 4)) != 0) {
                    failure.append("风向传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 4)) != 0) {
                    warn.append("风向预警,");
                    hm.put("warn-wind-direction", 1);
                } else hm.put("warn-wind-direction", 0);
                useMap.put("wind-direction", 1);
                hm.put("use-wind-direction", 1);
            } else {
                index += 2 * 2;
                useMap.put("wind-direction", 0);
                hm.put("use-wind-direction", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 5)) != 0) {
                hm.put("rainfall", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 5)) != 0) {
                    communication.append("雨量传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 5)) != 0) {
                    failure.append("雨量传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 5)) != 0) {
                    warn.append("雨量预警,");
                    hm.put("warn-rainfall", 1);
                } else hm.put("warn-rainfall", 0);
                useMap.put("rainfall", 1);
                hm.put("use-rainfall", 1);
            } else {
                index += 2 * 2;
                useMap.put("rainfall", 0);
                hm.put("use-rainfall", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 6)) != 0) {
                hm.put("air-pressure", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16))
                        .multiply(BigDecimal.valueOf(0.01)
                                .setScale(2, RoundingMode.HALF_UP)));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 6)) != 0) {
                    communication.append("气压传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 6)) != 0) {
                    failure.append("气压传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 6)) != 0) {
                    warn.append("气压预警,");
                    hm.put("warn-air-pressure", 1);
                } else hm.put("warn-air-pressure", 0);
                useMap.put("air-pressure", 1);
                hm.put("use-air-pressure", 1);
            } else {
                index += 2 * 2;
                useMap.put("air-pressure", 0);
                hm.put("use-air-pressure", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 7)) != 0) {
                hm.put("co2", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 7)) != 0) {
                    communication.append("CO2传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 7)) != 0) {
                    failure.append("CO2传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 7)) != 0) {
                    warn.append("CO2预警,");
                    hm.put("warn-co2", 1);
                } else hm.put("warn-co2", 0);
                useMap.put("co2", 1);
                hm.put("use-co2", 1);
            } else {
                index += 2 * 2;
                useMap.put("co2", 0);
                hm.put("use-co2", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 8)) != 0) {
                hm.put("ultraviolet", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 8)) != 0) {
                    communication.append("紫外线传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 8)) != 0) {
                    failure.append("紫外线传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 8)) != 0) {
                    warn.append("紫外线强度预警,");
                    hm.put("warn-ultraviolet", 1);
                } else hm.put("warn-ultraviolet", 0);
                useMap.put("ultraviolet", 1);
                hm.put("warn-ultraviolet", 1);
            } else {
                index += 2 * 2;
                useMap.put("ultraviolet", 0);
                hm.put("warn-ultraviolet", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 9)) != 0) {
                // short st = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                int tempStInt = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                short st = (short) tempStInt;
                hm.put("10cm-soil-temperature", BigDecimal.valueOf(st).multiply(BigDecimal.valueOf(0.01)).setScale(2, RoundingMode.HALF_UP));
                hm.put("10cm-soil-humidity", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16)).multiply(BigDecimal.valueOf(0.01))
                        .setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 9)) != 0) {
                    communication.append("10cm土壤传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 9)) != 0) {
                    failure.append("10cm土壤传感器故障,");
                } else {
                    if ((sensorWarnStatus & (int) Math.pow(2, 9)) != 0) {
                        warn.append("10cm土壤温度预警,");
                        hm.put("warn-10cm-soil-temperature", 1);
                    } else hm.put("warn-10cm-soil-temperature", 0);
                    if ((sensorWarnStatus & (int) Math.pow(2, 10)) != 0) {
                        warn.append("10cm土壤湿度预警,");
                        hm.put("warn-10cm-soil-humidity", 1);
                    } else hm.put("warn-10cm-soil-humidity", 0);
                }
                useMap.put("10cm-soil-temperature", 1);
                useMap.put("10cm-soil-humidity", 1);
                hm.put("use-10cm-soil-temperature", 1);
                hm.put("use-10cm-soil-humidity", 1);
            } else {
                index += 2 * 2;
                index += 2 * 2;
                useMap.put("10cm-soil-temperature", 0);
                useMap.put("10cm-soil-humidity", 0);
                hm.put("use-10cm-soil-temperature", 0);
                hm.put("use-10cm-soil-humidity", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 10)) != 0) {
                // short st = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                int tempStInt = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                short st = (short) tempStInt;
                hm.put("20cm-soil-temperature", BigDecimal.valueOf(st).multiply(BigDecimal.valueOf(0.01).setScale(2, RoundingMode.HALF_UP)));
                hm.put("20cm-soil-humidity", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16)).multiply(BigDecimal.valueOf(0.01)));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 10)) != 0) {
                    communication.append("20cm土壤传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 10)) != 0) {
                    failure.append("20cm土壤传感器故障,");
                } else {
                    //
                    if ((sensorWarnStatus & (int) Math.pow(2, 11)) != 0) {
                        warn.append("20cm土壤温度预警,");
                        hm.put("warn-20cm-soil-temperature", 1);
                    } else hm.put("warn-20cm-soil-temperature", 0);
                    //
                    if ((sensorWarnStatus & (int) Math.pow(2, 12)) != 0) {
                        warn.append("20cm土壤湿度预警,");
                        hm.put("warn-20cm-soil-humidity", 1);
                    } else hm.put("warn-20cm-soil-humidity", 0);
                }
                //
                useMap.put("20cm-soil-temperature", 1);
                useMap.put("20cm-soil-humidity", 1);
                hm.put("use-20cm-soil-temperature", 1);
                hm.put("use-20cm-soil-humidity", 1);
            } else {
                index += 2 * 2;
                index += 2 * 2;
                useMap.put("20cm-soil-temperature", 0);
                useMap.put("20cm-soil-humidity", 0);
                hm.put("use-20cm-soil-temperature", 0);
                hm.put("use-20cm-soil-humidity", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 11)) != 0) {
                // Short st = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                int tempStInt = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                short st = (short) tempStInt; // 使用 short 基础类型
                hm.put("30cm-soil-temperature", BigDecimal.valueOf(st * 0.01).setScale(2, RoundingMode.HALF_UP));
                hm.put("30cm-soil-humidity", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16)).multiply(BigDecimal.valueOf(0.01))
                        .setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 11)) != 0) {
                    communication.append("30cm土壤传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 11)) != 0) {
                    failure.append("30cm土壤传感器故障,");
                } else {
                    //
                    if ((sensorWarnStatus & (int) Math.pow(2, 13)) != 0) {
                        warn.append("30cm土壤温度预警,");
                        hm.put("warn-30cm-soil-temperature", 1);
                    } else hm.put("warn-30cm-soil-temperature", 0);
                    //
                    if ((sensorWarnStatus & (int) Math.pow(2, 14)) != 0) {
                        warn.append("30cm土壤湿度预警,");
                        hm.put("warn-30cm-soil-humidity", 1);
                    } else hm.put("warn-30cm-soil-humidity", 0);
                }
                //
                useMap.put("30cm-soil-temperature", 1);
                useMap.put("30cm-soil-humidity", 1);
                hm.put("use-30cm-soil-temperature", 1);
                hm.put("use-30cm-soil-humidity", 1);
            } else {
                index += 2 * 2;
                index += 2 * 2;
                useMap.put("30cm-soil-temperature", 0);
                useMap.put("30cm-soil-humidity", 0);
                hm.put("use-30cm-soil-temperature", 0);
                hm.put("use-30cm-soil-humidity", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 12)) != 0) {
                // Short st = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                int tempStInt = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                short st = (short) tempStInt; // 使用 short 基础类型
                hm.put("40cm-soil-temperature", BigDecimal.valueOf(st * 0.01).setScale(2, RoundingMode.HALF_UP));
                hm.put("40cm-soil-humidity", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 12)) != 0) {
                    communication.append("40cm土壤传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 12)) != 0) {
                    failure.append("40cm土壤传感器故障,");
                } else {
                    if ((sensorWarnStatus & (int) Math.pow(2, 15)) != 0) {
                        warn.append("40cm土壤温度预警,");
                        hm.put("warn-40cm-soil-temperature", 1);
                    } else hm.put("warn-40cm-soil-temperature", 0);
                    if ((sensorWarnStatus & (int) Math.pow(2, 16)) != 0) {
                        warn.append("40cm土壤湿度预警,");
                        hm.put("warn-40cm-soil-humidity", 1);
                    } else hm.put("warn-40cm-soil-humidity", 0);
                }
                useMap.put("40cm-soil-temperature", 1);
                useMap.put("40cm-soil-humidity", 1);
                hm.put("use-40cm-soil-temperature", 1);
                hm.put("use-40cm-soil-humidity", 1);
            } else {
                index += 2 * 2;
                index += 2 * 2;
                useMap.put("40cm-soil-temperature", 0);
                useMap.put("40cm-soil-humidity", 0);
                hm.put("use-40cm-soil-temperature", 0);
                hm.put("use-40cm-soil-humidity", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 13)) != 0) {
                // Short st = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                int tempStInt = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                short st = (short) tempStInt; // 使用 short 基础类型
                hm.put("50cm-soil-temperature", BigDecimal.valueOf(st * 0.01).setScale(2, RoundingMode.HALF_UP));
                hm.put("50cm-soil-humidity", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 13)) != 0) {
                    communication.append("50cm土壤传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 13)) != 0) {
                    failure.append("50cm土壤传感器故障,");
                } else {
                    if ((sensorWarnStatus & (int) Math.pow(2, 17)) != 0) {
                        warn.append("50cm土壤温度预警,");
                        hm.put("warn-50cm-soil-temperature", 1);
                    } else hm.put("warn-50cm-soil-temperature", 0);
                    if ((sensorWarnStatus & (int) Math.pow(2, 18)) != 0) {
                        warn.append("50cm土壤湿度预警,");
                        hm.put("warn-50cm-soil-humidity", 1);
                    } else hm.put("warn-50cm-soil-humidity", 0);
                }
                useMap.put("50cm-soil-temperature", 1);
                useMap.put("50cm-soil-humidity", 1);
                hm.put("use-50cm-soil-temperature", 1);
                hm.put("use-50cm-soil-humidity", 1);
            } else {
                index += 2 * 2;
                index += 2 * 2;
                useMap.put("50cm-soil-temperature", 0);
                useMap.put("50cm-soil-humidity", 0);
                hm.put("use-50cm-soil-temperature", 0);
                hm.put("use-50cm-soil-humidity", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 14)) != 0) {
                // Short st = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                int tempStInt = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                short st = (short) tempStInt; // 使用 short 基础类型
                hm.put("60cm-soil-temperature", BigDecimal.valueOf(st * 0.01).setScale(2, RoundingMode.HALF_UP));
                hm.put("60cm-soil-humidity", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 14)) != 0) {
                    communication.append("60cm土壤传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 14)) != 0) {
                    failure.append("60cm土壤传感器故障,");
                } else {
                    if ((sensorWarnStatus & (int) Math.pow(2, 19)) != 0) {
                        warn.append("60cm土壤温度预警,");
                        hm.put("warn-60cm-soil-temperature", 1);
                    } else hm.put("warn-60cm-soil-temperature", 0);
                    if ((sensorWarnStatus & (int) Math.pow(2, 20)) != 0) {
                        warn.append("60cm土壤湿度预警,");
                        hm.put("warn-60cm-soil-humidity", 1);
                    } else hm.put("warn-60cm-soil-humidity", 0);
                }
                useMap.put("60cm-soil-temperature", 1);
                useMap.put("60cm-soil-humidity", 1);
                hm.put("use-60cm-soil-temperature", 1);
                hm.put("use-60cm-soil-humidity", 1);
            } else {
                index += 2 * 2;
                index += 2 * 2;
                useMap.put("60cm-soil-temperature", 0);
                useMap.put("60cm-soil-humidity", 0);
                hm.put("use-60cm-soil-temperature", 0);
                hm.put("use-60cm-soil-humidity", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 15)) != 0) {
                hm.put("soil-electric-conductivity", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 15)) != 0) {
                    communication.append("土壤电导率传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 15)) != 0) {
                    failure.append("土壤电导率传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 21)) != 0) {
                    warn.append("土壤电导率预警,");
                    hm.put("warn-soil-electric-conductivity", 1);
                } else hm.put("warn-soil-electric-conductivity", 0);

                useMap.put("soil-electric-conductivity", 1);
                hm.put("use-soil-electric-conductivity", 1);
            } else {
                index += 2 * 4;
                useMap.put("soil-electric-conductivity", 0);
                hm.put("use-soil-electric-conductivity", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 16)) != 0) {
                hm.put("soil-ph", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 16)) != 0) {
                    communication.append("土壤PH传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 16)) != 0) {
                    failure.append("土壤PH传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 22)) != 0) {
                    warn.append("土壤PH预警,");
                    hm.put("warn-soil-ph", 1);
                } else hm.put("warn-soil-ph", 0);
                useMap.put("soil-ph", 1);
                hm.put("use-soil-ph", 1);
            } else {
                index += 2 * 2;
                useMap.put("soil-ph", 0);
                hm.put("use-soil-ph", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 17)) != 0) {
                hm.put("evaporation", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 17)) != 0) {
                    communication.append("蒸发量传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 17)) != 0) {
                    failure.append("蒸发量传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 23)) != 0) {
                    warn.append("蒸发量预警,");
                    hm.put("warn-evaporation", 1);
                } else hm.put("warn-evaporation", 0);
                useMap.put("evaporation", 1);
                hm.put("use-evaporation", 1);
            } else {
                index += 2 * 2;
                useMap.put("evaporation", 0);
                hm.put("use-evaporation", 0);
            }
            // warn_10cm_soil_temperature  warn_10cm_soil_humidity
            if ((sensorEnabledStatus & (int) Math.pow(2, 18)) != 0) {
                // short st = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                int tempStInt = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
                short st = (short) tempStInt;
                //BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16)).multiply(BigDecimal.valueOf(0.01)).setScale(2, RoundingMode.HALF_UP)
                hm.put("dew-point",BigDecimal.valueOf(st).multiply(BigDecimal.valueOf(0.01)).setScale(2, RoundingMode.HALF_UP));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 18)) != 0) {
                    communication.append("露点传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 18)) != 0) {
                    failure.append("露点传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 24)) != 0) {
                    warn.append("露点预警,");
                    hm.put("warn-dew-point", 1);
                } else hm.put("warn-dew-point", 0);
                useMap.put("dew-point", 1);
                hm.put("use-dew-point", 1);
            } else {
                index += 2 * 2;
                useMap.put("dew-point", 0);
                hm.put("use-dew-point", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 19)) != 0) {
                hm.put("par", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 19)) != 0) {
                    communication.append("光合有效辐射传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 19)) != 0) {
                    failure.append("光合有效辐射传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 25)) != 0) {
                    warn.append("光合有效辐射预警,");
                    hm.put("warn-par", 1);
                } else hm.put("warn-par", 0);
                useMap.put("par", 1);
                hm.put("use-par", 1);
            } else {
                index += 2 * 2;
                useMap.put("par", 0);
                hm.put("use-par", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 20)) != 0) {
                hm.put("ghi", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 20)) != 0) {
                    communication.append("总辐射传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 20)) != 0) {
                    failure.append("总辐射传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 26)) != 0) {
                    warn.append("总辐射预警,");
                    hm.put("warn-ghi", 1);
                } else hm.put("warn-ghi", 0);
                useMap.put("ghi", 1);
                hm.put("use-ghi", 1);
            } else {
                index += 2 * 2;
                useMap.put("ghi", 0);
                hm.put("use-ghi", 0);
            }

            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 21)) != 0) {
                hm.put("notice", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 21)) != 0) {
                    communication.append("噪音传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 21)) != 0) {
                    failure.append("噪音传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 27)) != 0) {
                    warn.append("噪音预警,");
                    hm.put("warn-notice", 1);
                } else hm.put("warn-notice", 0);
                useMap.put("notice", 1);
                hm.put("use-notice", 1);
            } else {
                index += 2 * 2;
                useMap.put("notice", 0);
                hm.put("use-notice", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 22)) != 0) {
                hm.put("pm25", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 22)) != 0) {
                    communication.append("PM2.5传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 22)) != 0) {
                    failure.append("PM2.5传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 28)) != 0) {
                    warn.append("PM2.5预警,");
                    hm.put("warn-pm25", 1);
                } else hm.put("warn-pm25", 0);
                useMap.put("pm25", 1);
                hm.put("use-pm25", 1);
            } else {
                index += 2 * 2;
                useMap.put("pm25", 0);
                hm.put("use-pm25", 0);
            }
            //
            if ((sensorEnabledStatus & (int) Math.pow(2, 23)) != 0) {
                hm.put("pm10", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
                if ((sensorCommunicationStatus & (int) Math.pow(2, 23)) != 0) {
                    communication.append("PM10传感器通讯异常,");
                } else if ((sensorFailureStatus & (int) Math.pow(2, 23)) != 0) {
                    failure.append("PM10传感器故障,");
                } else if ((sensorWarnStatus & (int) Math.pow(2, 29)) != 0) {
                    warn.append("PM10预警,");
                    hm.put("warn-pm10", 1);
                } else hm.put("warn-pm10", 0);
                useMap.put("pm10", 1);
                hm.put("use-pm10", 1);
            } else {
                index += 2 * 2;
                useMap.put("pm10", 0);
                hm.put("use-pm10", 0);
            }
            //氮含量 260-264
            Integer N = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("N",N);
            //磷含量
            Integer P = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("P",P);
            //钾含量
            Integer K = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("K",K);
            // 土壤张力15cm
            Integer soilTension15cm = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("soilTension15cm",soilTension15cm);
            //土壤张力30cm
            Integer soilTension30cm = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("soilTension30cm",soilTension30cm);
            //土壤张力45cm
            Integer soilTension45cm = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("soilTension45cm",soilTension45cm);
            hm.put("communication", StrUtil.isNotEmpty(communication) ? communication.deleteCharAt(communication.length() - 1).toString() : 0);
            hm.put("failure", StrUtil.isNotEmpty(failure) ? failure.deleteCharAt(failure.length() - 1).toString() : "");
            hm.put("warn", StrUtil.isNotEmpty(warn) ? warn.deleteCharAt(warn.length() - 1).toString() : "");
            hm.put("use-state", useMap);
            hm.put("value", str);
            //
            index += 2 * 13;
            return hm;
        } catch (Exception e) {
            log.error("解析传感器数据异常" + str + " " + e.getMessage() + " " + e);
            e.printStackTrace();
            return hm;
        }
    }

    public static void main(String[] args) {
        //System.out.println(Integer.parseInt("000307CD",16));;
        // 000307CD
        //parseControlData("6830000022806885880001902F0A0D1A04187F4FD4E70000000000002404220002EAD355D05205002281" +
        //        "CD0703000000000001D34E04007A572E00A101DC73040057050000D7981C99A398F50" +
        //        "A0000BC0A00008F0A0000000000000000030018001CC42E00DC0F00000000000000002A0D061A041893000000630018000900A01300003500000000007800140100000000CA5416");
    }

    //解析灌溉控制器
    public static HashMap<String, Object> parseControlData(String str) {
        HashMap<String, Object> hm = new HashMap<>();
        try {
            int index = 0;
            index += 2;
            hm.put("no", CommonUtil.reverse(str.substring(index, index += 2 * 5)));
            index += 2;
            hm.put("control-code", str.substring(index, index += 2));
            hm.put("length", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
            String function_code = CommonUtil.reverse(str.substring(index, index += 2 * 2));
            hm.put("function-code", function_code);
            log.info(LocalDateTime.now() + " " + hm.toString() + " " + str);
            String time = CommonUtil.reverse(str.substring(index, index += 2 * 6));
            LocalDate localDate = LocalDate.now();
            int year = Integer.parseInt(time.substring(0, 2), 16);
            int month = Integer.parseInt(time.substring(2, 4), 16);
            int day = Integer.parseInt(time.substring(4, 6), 16);
            int hour = Integer.parseInt(time.substring(6, 8), 16);
            int minute = Integer.parseInt(time.substring(8, 10), 16);
            int second = Integer.parseInt(time.substring(10, 12), 16);
            if (year <= 0 || month <= 0 || day <= 0) {
                throw new Exception("时间格式错误");
            }
            String timeStr = String.valueOf(localDate.getYear()).substring(0, 2) + (year < 10 ? ("0" + year) : year) + "-" +
                    (month < 10 ? ("0" + month) : month) + "-" +
                    (day < 10 ? ("0" + day) : day)
                    + " " + (hour < 10 ? ("0" + hour) : hour) + ":" +
                    (minute < 10 ? ("0" + minute) : minute) + ":" +
                    (second < 10 ? ("0" + second) : second);
            //
            hm.put("time", timeStr);
            hm.put("network-status", Long.parseLong(str.substring(index, index += 2), 16));
            hm.put("signal-strength", Long.parseLong(str.substring(index, index += 2), 16));
            hm.put("online-times", Long.parseLong(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16));
            int communicationStatus = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("communication-status", communicationStatus);
            //
            StringBuilder communicationStatusInfo = new StringBuilder();
            if ((communicationStatus & (int) Math.pow(2, 0)) != 0) {
                communicationStatusInfo.append("网络异常,");
            }
            if ((communicationStatus & (int) Math.pow(2, 1)) != 0) {
                communicationStatusInfo.append("电表异常,");
            }
            if ((communicationStatus & (int) Math.pow(2, 2)) != 0) {
                communicationStatusInfo.append("水表异常,");
            }
            if ((communicationStatus & (int) Math.pow(2, 3)) != 0) {
                communicationStatusInfo.append("FLASH,");
            }
            if ((communicationStatus & (int) Math.pow(2, 4)) != 0) {
                communicationStatusInfo.append("EEPROM,");
            }
            if ((communicationStatus & (int) Math.pow(2, 5)) != 0) {
                communicationStatusInfo.append("RFID,");
            }
            if ((communicationStatus & (int) Math.pow(2, 6)) != 0) {
                communicationStatusInfo.append("WWDGT,");
            }
            hm.put("communication-status-info", StrUtil.isNotEmpty(communicationStatusInfo) ? communicationStatusInfo.deleteCharAt(communicationStatusInfo.length() - 1).toString() : "");
            //
            int runningStatus = Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16);
            hm.put("running-status", runningStatus);
            StringBuilder runningStatusInfo = new StringBuilder();
            if ((runningStatus & (int) Math.pow(2, 0)) != 0) {
                runningStatusInfo.append("水泵异常,");
            }
            if ((runningStatus & (int) Math.pow(2, 1)) != 0) {
                runningStatusInfo.append("缺相,");
            }
            if ((runningStatus & (int) Math.pow(2, 2)) != 0) {
                runningStatusInfo.append("欠压,");
            }
            if ((runningStatus & (int) Math.pow(2, 3)) != 0) {
                runningStatusInfo.append("过压,");
            }
            if ((runningStatus & (int) Math.pow(2, 4)) != 0) {
                runningStatusInfo.append("单相失流,");
            }
            if ((runningStatus & (int) Math.pow(2, 5)) != 0) {
                runningStatusInfo.append("过流,");
            }
            if ((runningStatus & (int) Math.pow(2, 6)) != 0) {
                runningStatusInfo.append("(轻载)水泵空转,");
            }
            if ((runningStatus & (int) Math.pow(2, 7)) != 0) {
                runningStatusInfo.append("掉电,");
            }
            if ((runningStatus & (int) Math.pow(2, 8)) != 0) {
                runningStatusInfo.append("电流不平衡,");
            }
            if ((runningStatus & (int) Math.pow(2, 9)) != 0) {
                runningStatusInfo.append("低于额定功率,");
            }
            if ((runningStatus & (int) Math.pow(2, 10)) != 0) {
                runningStatusInfo.append("单相负电流(堵转),");
            }
            if ((runningStatus & (int) Math.pow(2, 11)) != 0) {
                runningStatusInfo.append("流量计异常,");
            }
            //

            hm.put("running-status-info", StrUtil.isNotEmpty(runningStatusInfo) ? runningStatusInfo.deleteCharAt(runningStatusInfo.length() - 1).toString() : "");
            hm.put("software-version", str.substring(index, index += 2 * 4));
            hm.put("data-status", Long.parseLong(str.substring(index, index += 2), 16));
            hm.put("card-id", str.substring(index, index += 2 * 4));
            hm.put("user-id", str.substring(index, index += 2 * 5));
            hm.put("balance", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("use-num", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
            hm.put("reserve", str.substring(index, index += 2 * 2));
            hm.put("bill-method", Integer.parseInt(str.substring(index, index += 2), 16));
            hm.put("start-electric-value", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("start-water-value", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("duration", Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16));
            hm.put("accumulative-electric-value", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("active-power", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("uab", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("ubc", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("uca", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            //
            short ia = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16);
            hm.put("ia", BigDecimal.valueOf(ia * 0.01).setScale(2, RoundingMode.HALF_UP));
            //
            short ib = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16);
            hm.put("ib", BigDecimal.valueOf(ib * 0.01).setScale(2, RoundingMode.HALF_UP));
            //
            short ic = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16);
            hm.put("ic", BigDecimal.valueOf(ic * 0.01).setScale(2, RoundingMode.HALF_UP));
            //
            hm.put("a-status", str.substring(index, index += 2 * 2));
            hm.put("b-status", str.substring(index, index += 2 * 2));
            hm.put("c-status", str.substring(index, index += 2 * 2));
            hm.put("all-status", str.substring(index, index += 2 * 2));
            hm.put("electric-meter-status", str.substring(index, index += 2 * 2));
            hm.put("accumulative-water-value", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("flow-velocity", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("water-level", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("water-pressure-1", Integer.parseInt(str.substring(index, index += 2 * 2)));
            hm.put("water-pressure-2", Integer.parseInt(str.substring(index, index += 2 * 2)));
            hm.put("begin-time", CommonUtil.reverse(str.substring(index, index += 2 * 6)));
            short rp = Short.parseShort(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16);
            hm.put("reactive-power", rp * 0.01);
            hm.put("power-factor", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("water-meter-status", str.substring(index, index += 2 * 2));
            hm.put("card-count", Integer.parseInt(str.substring(index, index += 2 * 2), 16));
            hm.put("this-charge", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 4)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("electric-price", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("water-price", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("time-price", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("set-power", BigDecimal.valueOf(Integer.parseInt(CommonUtil.reverse(str.substring(index, index += 2 * 2)), 16) * 0.01).setScale(2, RoundingMode.HALF_UP));
            hm.put("electricity-threshold", Integer.parseInt(str.substring(index, index += 2), 16));
            hm.put("enable-status", Integer.parseInt(str.substring(index, index += 2), 16));
            hm.put("hardware-version", str.substring(index, index += 2 * 4));
            return hm;
        } catch (Exception e) {
            log.error("灌溉控制器解析错误" + str + " " + e.getMessage() + " " + e);
            return hm;
        }
    }

    public static String computeWindDirection(Integer Angle) {

        String strDirection = "";
        int i = 0;
        if ((Angle >= 34875 && Angle < 36000) || Angle < 1125) {
            i = 0;
        } else if (Angle >= 36000) {
            i = 16;
        } else {
            for (i = 0; i < 15; i++) {
                if ((Angle >= (1125 + i * 2250)) && (Angle < (1125 + (i + 1) * 2250))) {
                    i += 1;
                    break;
                }
            }
        }
        switch (i + 1) {
            case 1:
                strDirection = "北";
                break;
            case 2:
                strDirection = "北东北";
                break;
            case 3:
                strDirection = "东北";
                break;
            case 4:
                strDirection = "东东北";
                break;
            case 5:
                strDirection = "东";
                break;
            case 6:
                strDirection = "东东南";
                break;
            case 7:
                strDirection = "东南";
                break;
            case 8:
                strDirection = "南东南";
                break;
            case 9:
                strDirection = "南";
                break;
            case 10:
                strDirection = "南西南";
                break;
            case 11:
                strDirection = "西南";
                break;
            case 12:
                strDirection = "西西南";
                break;
            case 13:
                strDirection = "西";
                break;
            case 14:
                strDirection = "西西北";
                break;
            case 15:
                strDirection = "西北";
                break;
            case 16:
                strDirection = "北西北";
                break;
            case 17:
                strDirection = "无风向";
                break;
        }
        return strDirection;
    }


    public static boolean checkToken(String msg, String nonce, String signature, String token) {
        byte[] paramB = new byte[token.length() + 8 + msg.length()];
        System.arraycopy(token.getBytes(), 0, paramB, 0, token.length());
        System.arraycopy(nonce.getBytes(), 0, paramB, token.length(), 8);
        System.arraycopy(msg.getBytes(), 0, paramB, token.length() + 8, msg.length());
        byte[] digest = mdInst.digest(paramB);
        String sig = Base64.getEncoder().encodeToString(digest);
        return sig.equals(signature.replace(' ', '+'));
    }

}
