package com.memory.dto;

import lombok.Data;

@Data
public class RedPassageParameterDTO {

    // 包头 (7 bytes)
    private String header;

    // 控制码 (1 byte)
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 数据标识 (4 bytes)
    private String dataIdentifier;

    //通道号（00：通道1，01：通道2，02：通道3，03：通道4）1字节
    private Integer passage;
    //类型（0：无，1：0-5V，2：0-10V,3：4-20mA，4：0.5-4.5V）1字节
    private Integer type;
    //传感器类型（0：无，1：压力1，2：压力2,3：液位）1字节
    private Integer sensorType;
    //最小值 2字节
    private Integer minValue;
    //最大值 2字节
    private Integer maxValue;
    //保护阈值倍数 2字节
    private String thresholdMultiple;
    //正常值 2字节
    private Integer normalValue;
    //动作模式（0：高于，1：低于，2：高于低于，3：保持）1字节
    private Integer actionMode;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;


}
