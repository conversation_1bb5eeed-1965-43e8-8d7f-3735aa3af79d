package com.memory.dto;

import lombok.Data;

/**
 * 设置阀门异常控制
 */
@Data
public class SetUpValveAbnormalControlDTO {

    // 包头 (7 bytes)
    //前后两个68是标识（也许是标识的是68协议）需要从后往前每一个字节取值才能得到最总的设备型号数据：8624000001（如01 00 00 24 86为8624000001）
    private String header;

    // 控制码 (1 byte)
    //固定值（文档写的是83H）其中H代表16进制
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 协议标识 (2 bytes)
    //固定值（文档写的是1DH）其中H代表16进制，但注意这里是两个字节，文档上没有标注，长度是从协议标识开始到SER结束
    private String dataIdentifier;

    // 状态字
    //占用1个字节
    private String statusWord;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;

}
