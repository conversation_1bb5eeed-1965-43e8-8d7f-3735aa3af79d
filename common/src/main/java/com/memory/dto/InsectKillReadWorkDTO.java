package com.memory.dto;

import lombok.Data;

/**
 * 杀虫灯读工作模式
 */
@Data
public class InsectKillReadWorkDTO {

    // 包头 (7 bytes)
    private String header;

    // 控制码 (1 byte)
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 数据标识 (4 bytes)
    private String dataIdentifier;

    //开关状态 1个字节（0：自动控制，1：强制开启，2：强制关闭，3：时空模式）
    private String switchStatus;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;





}
