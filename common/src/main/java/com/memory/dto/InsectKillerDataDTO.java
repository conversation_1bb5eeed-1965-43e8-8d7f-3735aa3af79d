package com.memory.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 灭虫灯数据
 */
@Data
public class InsectKillerDataDTO {
    // 包头 (7 bytes)
    private String header;

    // 控制码 (1 byte)
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 数据标识 (4 bytes)
    private String dataIdentifier;

    // 秒分时日月年 HEX (6 bytes)
    private String dateTimeHex;
    private LocalDateTime dateTime;

    // 网络状态 (1 byte)
    private int networkStatus;

    // 信号强度 (1 byte)
    private int signalStrength;

    // 网络在线时长 (4 bytes)
    private long onlineDuration;

    // 设备运行状态 (2 bytes)
    private String deviceStatus;

    // 主动上传时间间隔 (2 bytes)
    private int uploadInterval;

    // 电源电压 (2 bytes, 单位 mV)
    private double powerVoltage;

    // 软件版本 (4 bytes)
    private String softwareVersion;

    // 硬件版本信息 (4 bytes)
    private String hardwareVersion;

    // 保留字段 (8 bytes)
    private String reserved;

    // 继电器控制模式 (1 byte)
    private int relayControlMode;

    // 执行启动状态 (1 byte)
    private int startStatus;

    // 实际启动状态 (1 byte)
    private int actualStatus;

    // 太阳能电压 (2 bytes, 单位 mV)
    private double solarVoltage;

    // 功率大小 (2 bytes, 单位 mW)
    private int powerValue;

    // 空气温度 (2 bytes, 单位 0.1°C)
    private double airTemperature;

    // 空气湿度 (2 bytes, 单位 0.1%)
    private double airHumidity;

    // 充电电流 (2 bytes, 单位 mA)
    private int chargeCurrent;

    // 保护温度上限 (1 byte, 单位 °C)
    private int protectionTempUpper;

    // 保护温度下限 (1 byte, 单位 °C)
    private int protectionTempLower;

    // 时控启动时间 (2 bytes)
    private String timedStart;

    // 时控结束时间 (2 bytes)
    private String timedEnd;

    // 上次启动时间 (2 bytes)
    private String lastTimedStart;

    //上次结束时间 (2 bytes)
    private String lastTimedEnd;

    // 保留字段 (8 bytes)
    private String reserved2;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;

}
