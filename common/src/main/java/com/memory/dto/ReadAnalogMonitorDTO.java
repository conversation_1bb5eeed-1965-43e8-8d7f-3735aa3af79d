package com.memory.dto;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模拟量监测实体类
 */

@Data
public class ReadAnalogMonitorDTO {

    // 包头 (7 bytes)
    private String header;

    // 控制码 (1 byte)
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 数据标识 (4 bytes)
    private String dataIdentifier;

    // 秒分时日月年 HEX (6 bytes)
    private String dateTimeHex;
    private LocalDateTime dateTime;

    // 网络状态 (1 byte)
    private int networkStatus;

    // 信号强度 (1 byte)
    private String signalStrength;

    // 网络在线时长 (4 bytes)
    private long onlineDuration;

    // 设备运行状态 (2 bytes)
    private int deviceStatus;

    // 主动上传时间间隔 (2 bytes)
    private int uploadInterval;

    // 电源电压 (2 bytes, 单位 mV)
    private double powerVoltage;

    // 软件版本 (4 bytes)
    private String softwareVersion;

    // 硬件版本信息 (4 bytes)
    private String hardwareVersion;

    // 保留字段 (8 bytes)
    private String reserved;

    // 无线参数 (10 bytes)
    private String wirelessParameter;

    // 第一路传感器类型 (1 bytes)
    private int firstSensorType;

    // 第一路数据 (2 bytes)
    private String firstData;

    // 第一路电压 (2 bytes)
    private double firstVoltage;

    // 第一路电流 (2 bytes)
    private double firstCurrent;

    // 第二路传感器类型 (1 bytes)
    private int secondSensorType;

    // 第二路数据 (2 bytes)
    private String secondData;

    // 第二路电压 (2 bytes)
    private double secondVoltage;

    // 第二路电流 (2 bytes)
    private double secondCurrent;

    // 开关检测1状态 (1 bytes)
    private String SwitchDetection1Status;

    // 开关检测2状态 (1 bytes)
    private String SwitchDetection2Status;

    // 第一路输出状态 (1 bytes)
    private String firstOutputStatus;

    // 第二路输出状态 (1 bytes)
    private String secondOutputStatus;

    // 模拟量输出百分比 (1 bytes)
    private String analogMonitorOutputPercentage;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;



}
