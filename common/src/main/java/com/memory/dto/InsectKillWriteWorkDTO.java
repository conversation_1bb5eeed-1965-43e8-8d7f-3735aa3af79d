package com.memory.dto;

import lombok.Data;

/**
 * 杀虫灯写工作模式
 */
@Data
public class InsectKillWriteWorkDTO {

    // 包头 (7 bytes)
    private String header;

    // 控制码 (1 byte)
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 数据标识 (4 bytes)
    private String dataIdentifier;

    //状态字
    private String statusWord;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;

}
