package com.memory.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 读阀门数据
 */
@Data
public class ReadValveDateDto {

    // 包头 (7 bytes)
    //前后两个68是标识（也许是标识的是68协议）需要从后往前每一个字节取值才能得到最总的设备型号数据：8624000001（如01 00 00 24 86为8624000001）
    private String header;

    // 控制码 (1 byte)
    //固定值（文档写的是83H）其中H代表16进制
    private String controlCode;

    // 数据长度 (1 byte)
    //固定值（文档写的是1DH）其中H代表16进制，但注意这里是两个字节，文档上没有标注，长度是从协议标识开始到SER结束
    private String dataLength;

    // 协议标识 (2 bytes)
    private String dataIdentifier;

    // 秒分时日月年 HEX (6 bytes)
    private String dateTimeHex;
    private LocalDateTime dateTime;

    // 网络状态 (1 byte)
    private int networkStatus;

    // 信号强度 (1 byte)
    private int signalStrength;


    // 网络在线时长 (4 bytes)
    private long onlineDuration;

    // 设备运行状态 (2 bytes)
    private int deviceStatus;

    // 主动上传时间间隔 (2 bytes)
    private int uploadInterval;

    // 电源电压 (2 bytes, 单位 mV)
    private double powerVoltage;

    // 软件版本 (4 bytes)
    private String softwareVersion;

    // 硬件版本信息 (4 bytes)
    private String hardwareVersion;

    // 保留字段 (8 bytes)
    // 25.07.09 本更为 4 个字节
    private String reserved;

  /*  // 阀门开度上限
    private String openUpperLimit;

    // 阀门开度下限
    private String openLowerLimit;*/

    // 太阳能电压，单位mV 小端数转大端数 再 16进制转10进制
    private int solarVoltage;

    // 控制模式 0:单控模式,1:时长控制模式,2:流量控制模式,3:轮询控制模式
    private int controlModel;

    // 控制执行状态 0:未执行，1:执行中，2:执行完成，3:执行中断，4:执行异常
    private int controlExecutionStatus;

    // 阀门预设开度 1字节
    private double valvePresetOpen;

    // 阀门设定开度 1字节
    private int valveSetOpen;

    // 阀门当前开度
    private double valveCurrentOpen;

    // 延迟控制时间间隔，单位：分钟
    //占位4个字节，单位：分钟，小端数据（注意转大端数)
    //据在进行16进制转10进制
    private String delayControlTimeInterval;

    // 时长控制模式持续时间
    //占位4个字节，单位：分钟，值需要大于0，小数据（注意转大数据在进行16进制转10进制）
    private String durationControlModeDuration;

    // 流量控制模式流量大小
    //占位4个字节，单位：m³，值需要大于0，小数据（注意转大数据在进行16进制转10进制）
    private String flowControlModeFlowSize;

    // 周期开关阀时间间隔
    //占位4个字节，单位：分钟，值需要大于0，小数据（注意转大数据在进行16进制转10进制）
    private String periodicOnOffValveTimeInterval;

    // 周期开关阀持续时间
    //占位4个字节，单位：分钟，值需要大于0，小数据(注意转大数据在进行16进制转10进制)
    private String periodicSwitchingValveTime;

    // 周期开关阀次数
    //占位2个字节，值需要大于0，小数据（注意转大数据在进行16进制转10进制）
    private int regularOnOffValveNumber;

    //当前流量 占用2个字节
    private String currentTraffic;

    //阀门压力 占用2个字节
    private String valvePressure;

    //阀门异常控制使能状态
    //如数据:AA00小端数据转为大端数据:00AA
    //将00AA转为二进制数值为：0000 0000 1010 1010
    //与表的对应关系为：
    //D15 D14  D13 D12  D11 D10  D9  D8   D7  D6   D5  D4   D3  D2   D1  D0
    // 0   0   0   0    0   0    0   0    1   0    1   0    1   0    1   0

    //D7+D6（设备gps异常）: 00:开阀，01：关阀，10：无操作，11：预设角度
    //D5+D4（设备欠压）: 00:开阀，01：关阀，10：无操作，11：预设角度
    //D3+D2（设备离线）: 00:开阀，01：关阀，10：无操作，11：预设角度
    //D1+D0（无线通讯异常）: 00:开阀，01：关阀，10：无操作，11：预设角度

    //所以D7与D6取值为10，D5与D4取值为10，D3与D2取值为10，D1与D0取值为10，
    //因此状态为：设备gps异常：无操作。设备欠压：无操作。设备离线：无操作。无线通讯异常：无操作
    private ValveAbnormalControlDIC valveAbnormalStatus;

    //阀门开机自检状态
    private String valveStartupSelfTestStatus;

    //异常预设开度
    private String abnormalPresetOpen;

    //阀门开度上限
    private String valveOpenUpper;

    //阀门开度下限
    private String valveOpenLower;

    //阀门累积工作时间，单位：分钟 小端数转大端数 再 16进制转10进制
    private String valveAccumulateWorkTime;

    //阀门累积阀开时间，单位：分钟 小端数转大端数 再 16进制转10进制
    private String valveAccumulateOpenTime;

    //阀门累计动作次数 小端数转大端数 再 16进制转10进制
    private String valveAccumulateActionsNumber;

    //太阳能充电电流，单位：mA
    private String solarChargCurrent;

    //保留 6个字节
    private String reserve;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;

    //电池电量 单位 :%
    private Integer batteryLevel;

    //流速 0.01m³/h
    private Integer velocity;
    //累计流量 0.01m³
    private Integer cumulativeTraffic;

    // 压力传感器电压值
    private String pressureSensorVoltage;

    // 压力传感器电流值
    private String pressureSensorCurrent;
}
