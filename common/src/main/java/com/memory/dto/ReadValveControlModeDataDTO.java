package com.memory.dto;

import lombok.Data;

/**
 *  设备读取命令返回实体类
 */
@Data
public class ReadValveControlModeDataDTO {

    // 包头 (7 bytes)
    //前后两个68是标识（也许是标识的是68协议）需要从后往前每一个字节取值才能得到最总的设备型号数据：8624000001（如01 00 00 24 86为8624000001）
    private String header;

    // 控制码 (1 byte)
    //固定值（文档写的是83H）其中H代表16进制
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 数据标识 (2 bytes)
    //固定值（文档写的是1DH）其中H代表16进制，但注意这里是两个字节，文档上没有标注，长度是从协议标识开始到SER结束
    private String dataIdentifier;

    // 是否强制切换，值1占位1个字节，0：非强制 1：强制切换
    private int isItMandatory;

    // 控制模式，值1占位1个字节 0：单控模式，1：时长控制模式，2流量控制模式，3：轮询控制模式
    private int controlModel;

    // 阀门预设开度
    //占位1个字节，单位：%，小数据（注意转大数据)
    //在进行16进制转10进制
    private int valvePresetOpen;

    // 延迟控制时间间隔，单位：分钟
    //占位4个字节，单位：分钟，小端数据（注意转大端数)
    //据在进行16进制转10进制
    private String delayControlTimeInterval;

    // 时长控制模式持续时间
    //占位4个字节，单位：分钟，值需要大于0，小数据（注意转大数据在进行16进制转10进制）
    private String durationControlModeDuration;

    // 流量控制模式流量大小
    //占位4个字节，单位：m³，值需要大于0，小数据（注意转大数据在进行16进制转10进制）
    private String flowControlModeFlowSize;

    // 周期开关阀时间间隔
    //占位4个字节，单位：分钟，值需要大于0，小数据（注意转大数据在进行16进制转10进制）
    private String periodicOnOffValveTimeInterval;

    // 周期开关阀持续时间
    //占位4个字节，单位：分钟，值需要大于0，小数据(注意转大数据在进行16进制转10进制)
    private String periodicSwitchingValveTime;

    // 周期开关阀次数
    //占位2个字节，值需要大于0，小数据（注意转大数据在进行16进制转10进制）
    private int regularOnOffValveNumber;

    // 同步上限开度使能
    //占用1个字节
    private String synchronizationLimitOpen;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;
}
