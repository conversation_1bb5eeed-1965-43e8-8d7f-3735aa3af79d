package com.memory.dto;

import lombok.Data;

/**
 * 读取阀门异常控制
 */
@Data
public class ReadValveAbnormalControlDTO {

    // 包头 (7 bytes)
    //前后两个68是标识（也许是标识的是68协议）需要从后往前每一个字节取值才能得到最总的设备型号数据：8624000001（如01 00 00 24 86为8624000001）
    private String header;

    // 控制码 (1 byte)
    //固定值（文档写的是83H）其中H代表16进制
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 协议标识 (2 bytes)
    //固定值（文档写的是1DH）其中H代表16进制，但注意这里是两个字节，文档上没有标注，长度是从协议标识开始到SER结束
    private String dataIdentifier;

    //阀门异常控制使能状态
    //如数据:AA00小端数据转为大端数据:00AA
    //将00AA转为二进制数值为：0000 0000 1010 1010
    //与表的对应关系为：
    //D15 D14  D13 D12  D11 D10  D9  D8   D7  D6   D5  D4   D3  D2   D1  D0
    // 0   0   0   0    0   0    0   0    1   0    1   0    1   0    1   0

    //D7+D6（设备gps异常）: 00:开阀，01：关阀，10：无操作，11：预设角度
    //D5+D4（设备欠压）: 00:开阀，01：关阀，10：无操作，11：预设角度
    //D3+D2（设备离线）: 00:开阀，01：关阀，10：无操作，11：预设角度
    //D1+D0（无线通讯异常）: 00:开阀，01：关阀，10：无操作，11：预设角度

    //所以D7与D6取值为10，D5与D4取值为10，D3与D2取值为10，D1与D0取值为10，
    //因此状态为：设备gps异常：无操作。设备欠压：无操作。设备离线：无操作。无线通讯异常：无操作
    private ValveAbnormalControlDIC valveAbnormalStatus;

    //异常预设开度
    private String abnormalPresetOpen;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;

}
