package com.memory.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 温室设备实体类
 */
@Data
public class ReadGreenhouseSensorsDTO {


    // 包头 (7 bytes)
    private String header;

    // 控制码 (1 byte)
    private String controlCode;

    // 数据长度 (1 byte)
    private String dataLength;

    // 数据标识 (4 bytes)
    private String dataIdentifier;


    // 秒分时日月年 HEX (6 bytes)
    private String dateTimeHex;
    private LocalDateTime dateTime;

    // 网络状态 (1 byte)
    private int networkStatus;

    // 信号强度 (1 byte)
    private int signalStrength;

    // 网络在线时长 (4 bytes)
    private long onlineDuration;

    // 设备运行状态 (2 bytes)
    private int deviceStatus;

    // 主动上传时间间隔 (2 bytes)
    private int uploadInterval;

    // 电源电压 (2 bytes, 单位 mV)
    private double powerVoltage;

    // 软件版本 (4 bytes)
    private String softwareVersion;

    // 硬件版本信息 (4 bytes)
    private String hardwareVersion;

    //传感器总数
    private int sensorsTotalNumber;

    //本包传感器总数
    private int thisPackagesensorsTotalNumber;

    // 保留字段 (8 bytes)
    private String reserved;

    //多条传感器
    private List<MultipleSensors> multipleSensors;

    // SER (1 byte)
    private String ser;

    // 校验位 (1 byte)
    private String checksum;

    // 结束符 (1 byte)
    private String end;

    private Integer Server1NetworkStatus;
    private Integer Server2NetworkStatus;


}
