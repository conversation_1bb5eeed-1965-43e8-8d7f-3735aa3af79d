package com.memory.dto;

/**
 * 设备类型枚举类对比
 */
public enum DeviceEnum {
    //智能控制器
    INTELLIGENCE_CONTROL("1752558222319931394","智能控制器"),
    //智能采集器 1837006601534402561
    INTELLIGENCE_GATHER("1752558352653733890","智能采集器"),
    //视频监控
    VIDEO_MONITOR("1766026837420449793","视频监控"),
    //水肥控制
    WATERANDFERTILIZER_CONTROL("1766027072393748481","水肥控制"),
    //虫情设备
    INSECT_DEVICE("1766027341059891202","虫情设备"),
    //虫情测报灯
    INSECT_ALARMLAMP("1766027688247599105","虫情测报灯"),
    //气象站
    WEATHER_STATION("1766028019626975233","气象站"),
    //智能采集控制器
    INTELLIGENT_COLLECTION_CONTROLLER("1790584104643731458","智能采集控制器"),
    //杀虫灯
    KILLINSECT_LAMP("1793463645414473729","杀虫灯"),
    //智能阀控器
    INTELLIGENT_VALVECONTROL("1793463683725246466","智能阀控器"),

    GREENHOUSE("1837006601534402561","温室大棚");


    String key;
    String value;


    DeviceEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
