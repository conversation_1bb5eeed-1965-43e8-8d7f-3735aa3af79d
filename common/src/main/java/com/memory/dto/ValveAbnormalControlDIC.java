package com.memory.dto;

import lombok.Data;

/**
 * 阀门异常控制使能状态字典
 *
 * //D7+D6（设备gps异常）: 00:开阀，01：关阀，10：无操作，11：预设角度
*  //D5+D4（设备欠压）: 00:开阀，01：关阀，10：无操作，11：预设角度
*  //D3+D2（设备离线）: 00:开阀，01：关阀，10：无操作，11：预设角度
*  //D1+D0（无线通讯异常）: 00:开阀，01：关阀，10：无操作，11：预设角度
 */
@Data
public class ValveAbnormalControlDIC {
    //设备GPS
    private String deviceGPS;
    //设备欠压
    private String deviceUndervoltage;
    //设备离线
    private String deviceOffline;
    //无线通讯异常
    private String wirelessAbnormality;
}
