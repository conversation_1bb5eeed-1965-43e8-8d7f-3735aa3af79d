package com.memory;

import com.memory.dto.*;
import lombok.extern.slf4j.Slf4j;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DataParser {

    // 读杀虫灯数据定义解析数据的长度
    private static final int EXPECTED_LENGTH = 76;

    //读工作模式字节长度
    private static final int  READWORK_LENGTH = 16;
    //写工作模式
    private static final int WRITEWORK_LENGTH = 16;

    //读取阀门控制模式的字节长度
    private static final int  VALVEREAD_LENGTH = 41;

    //设置阀门控制模式的字节长度
    private static final int  SETUPVALVE_LENGTH = 17;

    //读阀控数据字节长度
    private static final int  READVALVE_LENGTH = 104;

    //重置阀门字节长度
    private static final int  RESETVALVE_LENGTH = 16;

    //读取阀门开机自检字节长度
    private static final int  READPOWERONSELFTEST_LENGTH = 17;

    //设置阀门开机自检字节长度
    private static final int  SETUPPOWERONSELFTEST_LENGTH = 16;

    //读取阀门异常控制字节长度
    private static final int  READVALVEABNORMALCONTROL_LENGTH = 18;

    //设置阀门异常控制字节长度
    private static final int  SETUPVALVEABNORMALCONTROL_LENGTH = 16;
    //读取阀门上下参数
    private static final int  READVALVEUPANDDOWNPARAMTER_LENGTH = 17;
    //设置阀门上下参数
    private static final int  SETUPVALVEUPANDDOWNPARAMTER_LENGTH = 16;
    /**
     * 读杀虫灯数据
     * @param hexString 十六进制字符
     * @return
     */

    public static InsectKillerDataDTO parse(String hexString) {
        log.info("灭虫灯传来的数据: {}", hexString);
        if (hexString.length() != EXPECTED_LENGTH * 2) {
            throw new IllegalArgumentException("数据长度不正确！");
        }

        InsectKillerDataDTO InsectKillerDataDTO = new InsectKillerDataDTO();
        // 包头 (7 bytes)
        InsectKillerDataDTO.setHeader(hexString.substring(0, 14));
        int index = 14;

        // 控制码+数据长度+数据标识 (5 bytes)
        InsectKillerDataDTO.setControlCode(hexString.substring(index, index + 2));
        Integer dataLength = Integer.parseInt(CommonUtil.reverse(hexString.substring(index + 2, index + 4)),16);
        InsectKillerDataDTO.setDataLength(dataLength.toString());
        InsectKillerDataDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index + 4, index + 10)));
        index += 10;

        // 校验数据长度是否正确
        /*int dataLength = Integer.parseInt(InsectKillerDataDTO.getDataLength(), 16);
        if (dataLength != (hexString.length() / 2 - 11)) {
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }*/
        int specifiedLength = Integer.parseInt(InsectKillerDataDTO.getDataLength()) * 2; // 数据长度是字节数，需乘以2换算为字符串长度
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（控制码+数据长度+数据标识）

        System.out.println("Specified length: " + specifiedLength);
        System.out.println("Actual length: " + actualLength);

        if (specifiedLength != actualLength) {
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        // 秒分时日月年 HEX (6 bytes)
        String hexDateTime = hexString.substring(index, index + 12);
        InsectKillerDataDTO.setDateTimeHex(hexDateTime);
        InsectKillerDataDTO.setDateTime(parseDateTime(hexDateTime));
        index += 12;

        // 网络状态 (1 byte)
        InsectKillerDataDTO.setNetworkStatus(Integer.parseInt(hexString.substring(index, index + 2), 16));
        index += 2;

        // 信号强度 (1 byte)
        InsectKillerDataDTO.setSignalStrength(Integer.parseInt(hexString.substring(index, index + 2), 16));
        index += 2;

        // 网络在线时长 (4 bytes)
        String substring = hexString.substring(index, index + 8);
//        log.info("网络在线时长数据: {}", substring);
//        log.info("网络在线时长Long处理结果为: {}", Long.parseLong(substring, 16));
        InsectKillerDataDTO.setOnlineDuration(Long.parseLong(CommonUtil.reverse(substring) , 16));
        index += 8;

        // 设备运行状态 (2 bytes)
        String status = "";
        Integer deviceStatus =   Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        String deviceStatusString = String.format("%16s", Integer.toBinaryString(deviceStatus)).replace(" ", "0");
        for (int i =0;i<deviceStatusString.length();i++){
            if (String.valueOf(deviceStatusString.charAt(i)).equals("1") ){
                int number = 15-i;
                status = number+",";
            }
        }
        if (status.equals("")){
            status = deviceStatus+"";
        }
        if (status.charAt(status.length()-1) == ',') status = status.substring(0, status.length()-1);
        InsectKillerDataDTO.setDeviceStatus(status);
        index += 4;

        // 主动上传时间间隔 (2 bytes)
        InsectKillerDataDTO.setUploadInterval(Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16));
        index += 4;

        // 电源电压 (2 bytes, 单位 mV)
        InsectKillerDataDTO.setPowerVoltage(Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16) / 1000.0);
        index += 4;

        // 软件版本 (4 bytes)
        InsectKillerDataDTO.setSoftwareVersion(hexString.substring(index, index + 8));
        index += 8;

        // 硬件版本信息 (4 bytes)
        InsectKillerDataDTO.setHardwareVersion(hexString.substring(index, index + 8));
        index += 8;

        // 保留字段 (8 bytes)
        InsectKillerDataDTO.setReserved(hexString.substring(index, index + 16));
        index += 16;

        // 继电器控制模式 (1 byte)
        InsectKillerDataDTO.setRelayControlMode(Integer.parseInt(hexString.substring(index, index + 2), 16));
        index += 2;

        // 执行启动状态 (1 byte)
        InsectKillerDataDTO.setStartStatus(Integer.parseInt(hexString.substring(index, index + 2), 16));
        index += 2;

        // 实际启动状态 (1 byte)
        InsectKillerDataDTO.setActualStatus(Integer.parseInt(hexString.substring(index, index + 2), 16));
        index += 2;

        // 太阳能电压 (2 bytes, 单位 mV)
        InsectKillerDataDTO.setSolarVoltage(Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16) / 1000.0);
        index += 4;

        // 功率大小 (2 bytes, 单位 mW)
        InsectKillerDataDTO.setPowerValue(Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16));
        index += 4;

        // 空气温度 (2 bytes, 单位 0.1°C)
        InsectKillerDataDTO.setAirTemperature(Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16) / 10.0);
        index += 4;

        // 空气湿度 (2 bytes, 单位 0.1%)
        InsectKillerDataDTO.setAirHumidity(Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16) / 10.0);
        index += 4;

        // 充电电流 (2 bytes, 单位 mA)
        InsectKillerDataDTO.setChargeCurrent(Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16));
        index += 4;

        // 保护温度上限 (1 byte, 单位 °C)
        InsectKillerDataDTO.setProtectionTempUpper(Integer.parseInt(hexString.substring(index, index + 2), 16));
        index += 2;

        // 保护温度下限 (1 byte, 单位 °C)
        InsectKillerDataDTO.setProtectionTempLower(Integer.parseInt(hexString.substring(index, index + 2), 16));
        index += 2;

        // 时控启动时间 (2 bytes)
        String startTimeHour = hexString.substring(index, index + 2);
        String startTimeMinute = hexString.substring(index+2, index + 4);
        InsectKillerDataDTO.setTimedStart(startTimeHour+":"+startTimeMinute);
        index += 4;

        // 时控结束时间 (2 bytes)
        String endTimeHour = hexString.substring(index, index + 2);
        String endTimeMinute = hexString.substring(index+2, index + 4);
        InsectKillerDataDTO.setTimedEnd(endTimeHour+":"+endTimeMinute);
        index += 4;
        //上次启动时间
        String lastStartTimeHour = hexString.substring(index, index + 2);
        String laststartTimeMinute = hexString.substring(index+2, index + 4);
        InsectKillerDataDTO.setLastTimedStart(lastStartTimeHour+":"+laststartTimeMinute);
        index += 4;
        //上次结束时间
        String lastEndTimeHour = hexString.substring(index, index + 2);
        String lastEndTimeMinute = hexString.substring(index+2, index + 4);
        InsectKillerDataDTO.setLastTimedEnd(lastEndTimeHour+":"+lastEndTimeMinute);
        index += 4;
        // 保留字段 (4 bytes)
        InsectKillerDataDTO.setReserved(hexString.substring(index, index + 8));
        index += 8;

        // 校验位+结束符 (3 bytes)
        InsectKillerDataDTO.setChecksum(hexString.substring(index, index + 6));

        // 校验报文的完整性
        // validateChecksum(hexString.substring(0, index), hexString.substring(index, index + 6));


        return InsectKillerDataDTO;
    }

    /**
     * 杀虫灯读工作模式
     * @param hexString 十六进制字符
     * @return
     */
    public static InsectKillReadWorkDTO inseckKillReadWorkParse(String hexString) {

        //打印日志
        log.info("杀虫灯读工作模式读取传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != READWORK_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建设备读取的实体类
        InsectKillReadWorkDTO insectKillReadWorkDTO = new InsectKillReadWorkDTO();
        // 包头 (7 bytes)
        insectKillReadWorkDTO.setHeader(hexString.substring(0,14));
        //设置下标
        int index = 14;
        //控制码 (1 byte)
        insectKillReadWorkDTO.setControlCode(hexString.substring(index,index+2));
        //
        index += 2;
        //
        //数据长度 (1 byte)（数据小端值转成大端值）然后16进制转10进制
        Integer ControlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        insectKillReadWorkDTO.setDataLength(ControlCode.toString());
        //
        index += 4;
        //
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (ControlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //数据标识 (2 bytes)
        insectKillReadWorkDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index=index+4;
        //开关状态 1个字节（0：自动控制，1：强制开启，2：强制关闭，3：时空模式）
        Integer switchStatus = Integer.parseInt(hexString.substring(index, index + 2),16);
        insectKillReadWorkDTO.setSwitchStatus(switchStatus.toString());
        //
        index += 2;
        //SER，序列号或标识符
        String ser = hexString.substring(index, index + 2);
        insectKillReadWorkDTO.setSer(ser);
        //
        index = index +2;
        //
        String checksum =  hexString.substring(index, index + 2);
        insectKillReadWorkDTO.setChecksum(checksum);
        //
        index = index +2;
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        insectKillReadWorkDTO.setEnd(end);

        return insectKillReadWorkDTO;

    }

    /**
     * 杀虫灯写工作模式
     * @param hexString  十六进制字符
     * @return
     */
    public static InsectKillWriteWorkDTO inseckKillWriteWorkParse(String hexString) {
        //打印日志
        log.info("杀虫灯写工作模式读取传来的数据: {}", hexString);

        if (hexString.length() != SETUPVALVE_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建设置阀门控制模式的实体类
        InsectKillWriteWorkDTO insectKillWriteWorkDTO = new InsectKillWriteWorkDTO();

        // 包头 (7 bytes)
        insectKillWriteWorkDTO.setHeader(hexString.substring(0,14));
        //设置下标
        int index = 14;
        //控制码 (1 byte)
        insectKillWriteWorkDTO.setControlCode(hexString.substring(index,index+2));
        //
        index = index + 2;
        //
        Integer dataLength = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)),16);
        insectKillWriteWorkDTO.setDataLength(dataLength.toString());

        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (dataLength*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        // 协议标识 (2 bytes)
        insectKillWriteWorkDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index, index + 4)));
        //
        index = index+4;

        //SER，序列号或标识符
        String ser = hexString.substring(index, index + 2);
        insectKillWriteWorkDTO.setSer(ser);
        //
        index = index +2;
        //
        String checksum =  hexString.substring(index, index + 2);
        insectKillWriteWorkDTO.setChecksum(checksum);
        //
        index = index +2;
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        insectKillWriteWorkDTO.setEnd(end);

        return insectKillWriteWorkDTO;
    }



    // 读取阀控控制模式解析
    public static ReadValveControlModeDataDTO readValveParse(String hexString) {
        //打印日志
        log.info("阀控控制模式读取传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != VALVEREAD_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建设备读取的实体类
        ReadValveControlModeDataDTO readingDataDTO = new ReadValveControlModeDataDTO();
        // 包头 (7 bytes)
        readingDataDTO.setHeader(hexString.substring(0,14));
        //设置下标
        int index = 14;
        //控制码 (1 byte)
        readingDataDTO.setControlCode(hexString.substring(index,index+2));
        //下标+2
        index = index+2;
        //数据长度 (1 byte)（数据小端值转成大端值）然后16进制转10进制
        Integer ControlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        readingDataDTO.setDataLength(ControlCode.toString());

        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (ControlCode*2 != actualLength){
                throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //数据标识 (2 bytes)
        readingDataDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index=index+4;
        //是否强制切换，值1占位1个字节，0：非强制 1：强制切换
        readingDataDTO.setIsItMandatory(Integer.parseInt(hexString.substring(index,index+2),16));
        //
        index = index+2;
        //控制模式，值1占位1个字节 0：单控模式，1：时长控制模式，2:流量控制模式，3：轮询控制模式
        readingDataDTO.setControlModel(Integer.parseInt(hexString.substring(index,index+2),16));
        //
        index = index+2;
        //阀门预设开度 先小端数据（注意转大端数据) 在进行16进制转10进制
        readingDataDTO.setValvePresetOpen(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+2)),16));
        //
        index = index+2;
        //延迟控制时间间隔，单位：分钟 小端数据（注意转大端数) 在进行16进制转10进制
        Integer delayControlTimeInterval = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 8)),16);
        readingDataDTO.setDelayControlTimeInterval(delayControlTimeInterval.toString());
        //
        index = index +8;
        //时长控制模式持续时间
        Integer durationControlModeDuration = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 8)),16);
        readingDataDTO.setDurationControlModeDuration(durationControlModeDuration.toString());
        //
        index = index +8;
        //流量控制模式流量大小
        Integer flowControlModeFlowSize = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 8)),16);
        readingDataDTO.setFlowControlModeFlowSize(flowControlModeFlowSize.toString());
        //
        index = index +8;
        //周期开关阀时间间隔
        Integer periodicOnOffValveTimeInterval = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 8)),16);
        readingDataDTO.setPeriodicOnOffValveTimeInterval(periodicOnOffValveTimeInterval.toString());
        //
        index = index +8;
        //周期开关阀持续时间
        Integer periodicSwitchingValveTime = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 8)),16);
        readingDataDTO.setPeriodicSwitchingValveTime(periodicSwitchingValveTime.toString());
        //
        index = index +8;
        //周期开关阀次数
        readingDataDTO.setRegularOnOffValveNumber(Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)),16));
        //
        index = index +4;
        //同步上限开度使能
        Integer synchronizationLimitOpen = Integer.parseInt(hexString.substring(index, index + 2), 16);
        readingDataDTO.setSynchronizationLimitOpen(synchronizationLimitOpen.toString());
        //
        index = index +2;
        //SER，序列号或标识符
        String ser = hexString.substring(index, index + 2);
        readingDataDTO.setSer(ser);
        //
        index = index +2;
        //
        String checksum =  hexString.substring(index, index + 2);
        readingDataDTO.setChecksum(checksum);
        //
        index = index +2;
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        readingDataDTO.setEnd(end);

        return readingDataDTO;

    }

    // 设置阀控控制模式解析
    public static SetUpValveControlModeDataDTO setUpValveParse(String hexString) {
        //打印日志
        log.info("阀控控制模式读取传来的数据: {}", hexString);

        if (hexString.length() != SETUPVALVE_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建设置阀门控制模式的实体类
        SetUpValveControlModeDataDTO setUpDataDTO = new SetUpValveControlModeDataDTO();

        // 包头 (7 bytes)
        setUpDataDTO.setHeader(hexString.substring(0,14));
        //设置下标
        int index = 14;
        //控制码 (1 byte)
        setUpDataDTO.setControlCode(hexString.substring(index,index+2));
        //
        index = index + 2;
        //
        Integer dataLength = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)),16);
        setUpDataDTO.setDataLength(dataLength.toString());
        //
        index = index+4;
        // 协议标识 (2 bytes)
        setUpDataDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index, index + 4)));
        //
        index = index+4;
        //控制执行状态
        Integer controlExecutionStatus =  Integer.parseInt(hexString.substring(index,index+2),16);
        setUpDataDTO.setControlExecutionStatus(controlExecutionStatus.toString());
        //
        index = index + 2;
        //状态字
        Integer statusWord =  Integer.parseInt(hexString.substring(index,index+2),16);
        setUpDataDTO.setStatusWord(statusWord.toString());
        //
        index = index + 2;
        //ser
        setUpDataDTO.setSer(hexString.substring(index,index+2));
        //
        index = index + 2;
        //
        setUpDataDTO.setChecksum(hexString.substring(index,index+2));
        //
        index = index + 2;
        //判断结尾符是否正确
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        setUpDataDTO.setEnd(end);

        return setUpDataDTO;
    }

    /**
     * 读取阀控数据解析方法（2025.07.09协议更新版本）
     * <p>
     * 解析阀门控制器上报的状态数据，数据包总长度为104字节（208个十六进制字符）。
     * 数据包采用小端序和大端序混合编码，部分字段需要进行字节序转换。
     * </p>
     *
     * <h3>数据包结构说明（总长度：104字节）：</h3>
     * <table border="1">
     * <tr><th>字段名称</th><th>索引位置</th><th>字节长度</th><th>数据格式</th><th>业务含义</th></tr>
     * <tr><td>包头</td><td>0-13 (字节位置0-6)</td><td>7字节</td><td>HEX字符串</td><td>数据包起始标识</td></tr>
     * <tr><td>控制码+数据长度+数据标识</td><td>14-23 (字节位置7-11)</td><td>5字节</td><td>混合格式</td><td>控制码+数据长度+协议标识</td></tr>
     * <tr><td>时间戳</td><td>24-35 (字节位置12-17)</td><td>6字节</td><td>HEX(秒分时日月年)</td><td>设备时间</td></tr>
     * <tr><td>网络状态</td><td>36-37 (字节位置18-18)</td><td>1字节</td><td>小端序HEX→大端序→十进制</td><td>网络连接状态</td></tr>
     * <tr><td>信号强度</td><td>38-39 (字节位置19-19)</td><td>1字节</td><td>HEX→十进制</td><td>信号强度值</td></tr>
     * <tr><td>在线时长</td><td>40-47 (字节位置20-23)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>网络在线时长(秒)</td></tr>
     * <tr><td>设备状态</td><td>48-51 (字节位置24-25)</td><td>2字节</td><td>HEX→十进制</td><td>设备运行状态</td></tr>
     * <tr><td>上传间隔</td><td>52-55 (字节位置26-27)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>主动上传时间间隔(秒)</td></tr>
     * <tr><td>电源电压</td><td>56-59 (字节位置28-29)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>电源电压值(mV)</td></tr>
     * <tr><td>软件版本</td><td>60-67 (字节位置30-33)</td><td>4字节</td><td>HEX字符串</td><td>软件版本号</td></tr>
     * <tr><td>硬件版本</td><td>68-75 (字节位置34-37)</td><td>4字节</td><td>HEX→十进制(分段解析)</td><td>硬件版本号</td></tr>
     * <tr><td>保留字段1</td><td>76-79 (字节位置38-39)</td><td>2字节</td><td>HEX字符串</td><td>预留字段</td></tr>
     * <tr><td>阀门开度上限</td><td>80-81 (字节位置40-40)</td><td>1字节</td><td>HEX→十进制</td><td>阀门开度上限值</td></tr>
     * <tr><td>阀门开度下限</td><td>82-83 (字节位置41-41)</td><td>1字节</td><td>HEX→十进制</td><td>阀门开度下限值</td></tr>
     * <tr><td>太阳能电压</td><td>84-87 (字节位置42-43)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>太阳能电压(mV)</td></tr>
     * </table>
     *
     * <h3>阀门控制相关字段：</h3>
     * <table border="1">
     * <tr><th>字段名称</th><th>索引位置</th><th>字节长度</th><th>数据格式</th><th>业务含义</th></tr>
     * <tr><td>控制模式</td><td>88-89 (字节位置44-44)</td><td>1字节</td><td>HEX→十进制</td><td>0:单控,1:时长控制,2:流量控制,3:轮询控制</td></tr>
     * <tr><td>执行状态</td><td>90-91 (字节位置45-45)</td><td>1字节</td><td>HEX→十进制</td><td>0:未执行,1:执行中,2:完成,3:中断,4:异常</td></tr>
     * <tr><td>预设开度</td><td>92-93 (字节位置46-46)</td><td>1字节</td><td>小端序HEX→大端序→十进制</td><td>阀门预设开度百分比</td></tr>
     * <tr><td>设定开度</td><td>94-95 (字节位置47-47)</td><td>1字节</td><td>小端序HEX→大端序→十进制</td><td>阀门设定开度百分比</td></tr>
     * <tr><td>当前开度</td><td>96-97 (字节位置48-48)</td><td>1字节</td><td>HEX→十进制</td><td>阀门当前开度百分比</td></tr>
     * <tr><td>延迟控制间隔</td><td>98-105 (字节位置49-52)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>延迟控制时间间隔(分钟)</td></tr>
     * <tr><td>时长控制持续时间</td><td>106-113 (字节位置53-56)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>时长控制模式持续时间</td></tr>
     * <tr><td>流量控制大小</td><td>114-121 (字节位置57-60)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>流量控制模式流量大小</td></tr>
     * <tr><td>周期开关间隔</td><td>122-129 (字节位置61-64)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>周期开关阀时间间隔</td></tr>
     * <tr><td>周期开关持续时间</td><td>130-137 (字节位置65-68)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>周期开关阀持续时间(分钟)</td></tr>
     * <tr><td>周期开关次数</td><td>138-141 (字节位置69-70)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>周期开关阀次数</td></tr>
     * <tr><td>当前流量</td><td>142-145 (字节位置71-72)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>当前流量值</td></tr>
     * <tr><td>阀门压力</td><td>146-149 (字节位置73-74)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>阀门压力值</td></tr>
     * <tr><td>流速</td><td>150-153 (字节位置75-76)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>流速值</td></tr>
     * <tr><td>累积流量</td><td>154-161 (字节位置77-80)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>累积流量值</td></tr>
     * </table>
     *
     * <h3>累积统计和其他字段：</h3>
     * <table border="1">
     * <tr><th>字段名称</th><th>索引位置</th><th>字节长度</th><th>数据格式</th><th>业务含义</th></tr>
     * <tr><td>累积工作时间</td><td>162-169 (字节位置81-84)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>阀门累积工作时间(分钟)</td></tr>
     * <tr><td>累积开启时间</td><td>170-177 (字节位置85-88)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>阀门累积开启时间(分钟)</td></tr>
     * <tr><td>累积动作次数</td><td>178-185 (字节位置89-92)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>阀门累计动作次数</td></tr>
     * <tr><td>太阳能充电电流</td><td>186-189 (字节位置93-94)</td><td>2字节</td><td>HEX→十进制</td><td>太阳能充电电流(mA)</td></tr>
     * <tr><td>保留字段2</td><td>190-201 (字节位置95-100)</td><td>6字节</td><td>HEX→十进制</td><td>预留字段</td></tr>
     * <tr><td>SER+校验位+结束符</td><td>202-207 (字节位置101-103)</td><td>3字节</td><td>HEX字符串</td><td>序列号+校验位+结束标识</td></tr>
     * </table>
     *
     * <h3>协议变更说明（2025.07.09更新）：</h3>
     * <ul>
     * <li><strong>新增字段</strong>：流速（位置75-76）、累积流量（位置77-80）</li>
     * <li><strong>位置调整</strong>：阀门开度上下限从位置79-80移动到40-41</li>
     * <li><strong>删除字段</strong>：异常控制状态、自检状态、异常预设开度</li>
     * <li><strong>保留字段调整</strong>：保留字段1从4字节调整为2字节</li>
     * </ul>
     *
     * <h3>特殊处理逻辑：</h3>
     * <ul>
     * <li><strong>电池电量计算</strong>：根据电源电压计算，公式为 (电压-3400)/800，范围0-100%</li>
     * <li><strong>硬件版本解析</strong>：4字节分别转换为十进制，用"-"连接</li>
     * <li><strong>字节序转换</strong>：使用CommonUtil.reverse()进行小端序到大端序转换</li>
     * <li><strong>时间解析</strong>：调用parseDateTime()方法解析6字节时间戳</li>
     * <li><strong>新增字段解析</strong>：流速和累积流量均使用小端序转换</li>
     * <li><strong>阀门开度处理</strong>：预设开度和设定开度使用小端序转换，当前开度和上下限直接转换</li>
     * </ul>
     *
     * @param hexString 十六进制字符串，长度必须为208个字符（104字节*2）
     * @return ReadValveDateDto 解析后的阀控数据传输对象
     * @throws IllegalArgumentException 当数据长度不正确或数据长度与报文指定长度不匹配时抛出
     * @see CommonUtil#reverse(String) 字节序转换工具方法
     * @see #parseDateTime(String) 时间解析方法
     * @since 1.0
     * <AUTHOR>
     * @version 2025.07.09 - 协议更新版本
     */
    public static ReadValveDateDto readValveDateParse(String hexString) {
        // 记录接收到的原始数据用于调试和问题排查
        log.info("阀控数据读取传来的数据: {}", hexString);

        // 数据长度校验：104字节 = 208个十六进制字符
        if (hexString.length() != READVALVE_LENGTH*2){
            log.error("数据长度不匹配！");
            throw new IllegalArgumentException("数据长度不正确！");
        }

        // 创建阀控读取的数据传输对象
        ReadValveDateDto readValveDateDto = new ReadValveDateDto();

        /* ========== 数据包头部解析 ========== */
        // 包头(索引0-13, 7字节, 字节位置0-6): 数据包起始标识，直接提取十六进制字符串
        readValveDateDto.setHeader(hexString.substring(0,14));

        // 初始化解析索引指针，从包头后开始
        int index = 14;

        // 控制码(索引14-15, 1字节, 字节位置7-7): 命令控制码，直接提取十六进制字符串
        readValveDateDto.setControlCode(hexString.substring(index,index+2));
        index = index+2;

        // 数据长度(索引16-19, 2字节, 字节位置8-9): 小端序转大端序后转十进制，表示数据域字节数
        Integer controlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        readValveDateDto.setDataLength(controlCode.toString());

        // 数据长度一致性校验：去掉包头、控制码、数据长度、校验位、结束符共12字节(24个字符)
        int actualLength = hexString.length() - 24;
        // 数据长度字段值*2应等于实际数据域字符串长度
        if (controlCode*2 != actualLength){
            log.error("数据长度与报文中指定的长度不匹配！");
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        index = index+4;

        // 协议标识(索引20-23, 2字节, 字节位置10-11): 小端序转大端序，标识协议版本
        readValveDateDto.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        index = index+4;
        /* ========== 时间戳解析 ========== */
        // 时间戳(索引24-35, 6字节, 字节位置12-17): 按秒分时日月年顺序排列的BCD编码时间
        String hexDateTime = hexString.substring(index, index + 12);
        readValveDateDto.setDateTimeHex(hexDateTime);  // 保存原始十六进制时间
        readValveDateDto.setDateTime(parseDateTime(hexDateTime));  // 解析为可读时间格式
        index = index+12;

        /* ========== 网络状态信息解析 ========== */
        // 网络状态(索引36-37, 1字节, 字节位置18-18): 小端序转大端序后转十进制，表示网络连接状态
        readValveDateDto.setNetworkStatus(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+2)),16));
        index = index+2;

        // 信号强度(索引38-39, 1字节, 字节位置19-19): 直接十六进制转十进制，表示信号强度值
        readValveDateDto.setSignalStrength(Integer.parseInt(hexString.substring(index,index+2),16));
        index = index+2;

        // 网络在线时长(索引40-47, 4字节, 字节位置20-23): 小端序转大端序后转十进制，单位为秒
        readValveDateDto.setOnlineDuration(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16));
        index = index+8;

        /* ========== 设备状态信息解析 ========== */
        // 设备运行状态(索引48-51, 2字节, 字节位置24-25): 直接十六进制转十进制，表示设备当前运行状态
        readValveDateDto.setDeviceStatus(Integer.parseInt(hexString.substring(index,index+4),16));
        index = index+4;

        // 主动上传时间间隔(索引52-55, 2字节, 字节位置26-27): 小端序转大端序后转十进制，单位为秒
        readValveDateDto.setUploadInterval(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16));
        index = index+4;
        /* ========== 电源电压和电池电量解析 ========== */
        // 电源电压(索引56-59, 2字节, 字节位置28-29): 小端序转大端序后转十进制，单位为毫伏(mV)
        double powerVoltage =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readValveDateDto.setPowerVoltage(powerVoltage);

        // 电池电量计算: 根据电源电压计算电池电量百分比
        // 计算公式: (电压 - 3400mV) / 800mV，结果范围0-1，转换为0-100%
        double batteryLevel = (powerVoltage - 3400) / 800;
        if (batteryLevel >=1) {
            batteryLevel = 100;  // 电量上限100%
        }
        else {
            batteryLevel = batteryLevel*100;  // 转换为百分比
        }
        readValveDateDto.setBatteryLevel((int)batteryLevel);
        index = index+4;

        /* ========== 版本信息解析 ========== */
        // 软件版本(索引60-67, 4字节, 字节位置30-33): 直接提取十六进制字符串作为版本标识
        readValveDateDto.setSoftwareVersion(hexString.substring(index,index+8));
        index = index+8;

        // 硬件版本(索引68-75, 4字节, 字节位置34-37): 每字节分别转十进制，用"-"连接组成版本号
        String version = hexString.substring(index,index+8);
        Integer version1 = Integer.parseInt(version.substring(0, 2), 16);  // 主版本号
        Integer version2 = Integer.parseInt(version.substring(2, 4), 16);  // 次版本号
        Integer version3 = Integer.parseInt(version.substring(4, 6), 16);  // 修订版本号
        Integer version4 = Integer.parseInt(version.substring(6, 8), 16);  // 构建版本号
        readValveDateDto.setHardwareVersion(version1+"-"+version2+"-"+version3+"-"+version4);
        index = index+8;
       //
        // 保留字段1(索引76-83, 4字节, 字节位置38-41): 协议预留字段，直接提取十六进制字符串
        // 25.07.09 保留字段1(索引76-79, 2字节, 字节位置38-39)
        readValveDateDto.setReserved(hexString.substring(index,index+4));
        index = index+4;
        // 25.07.09 阀门开度上限(索引80-81, 1字节, 字节位置40-40)
        readValveDateDto.setValveOpenUpper(Integer.parseInt((hexString.substring(index,index+2)),16)+"");
        index = index+2;
        // 25.07.09 阀门开度下限(索引82-83, 1字节, 字节位置41-41)
        readValveDateDto.setValveOpenLower(Integer.parseInt((hexString.substring(index,index+2)),16)+"");
        index = index+2;

        // 太阳能电压(索引84-87, 2字节, 字节位置42-43): 小端序转大端序后转十进制，单位为毫伏(mV)
        readValveDateDto.setSolarVoltage(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16));
        index = index+4;
        /* ========== 阀门控制模式和状态解析 ========== */
        // 控制模式(索引88-89, 1字节, 字节位置44-44): 直接十六进制转十进制
        // 0:单控模式, 1:时长控制模式, 2:流量控制模式, 3:轮询控制模式
        readValveDateDto.setControlModel(Integer.parseInt(hexString.substring(index,index+2),16));
        index = index+2;

        // 控制执行状态(索引90-91, 1字节, 字节位置45-45): 直接十六进制转十进制
        // 0:未执行, 1:执行中, 2:执行完成, 3:执行中断, 4:执行异常
        readValveDateDto.setControlExecutionStatus(Integer.parseInt(hexString.substring(index,index+2),16));
        index = index+2;

        /* ========== 阀门开度信息解析 ========== */
        // 阀门预设开度(索引92-93, 1字节, 字节位置46-46): 直接十六进制转十进制，表示预设开度百分比(0-100)
        readValveDateDto.setValvePresetOpen(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+2)),16));
        index = index+2;

        // 阀门设定开度(索引94-95, 1字节, 字节位置47-47): 直接十六进制转十进制，表示设定开度百分比(0-100)
        readValveDateDto.setValveSetOpen(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+2)),16));
        index = index+2;

        // 阀门当前开度(索引96-97, 1字节, 字节位置48-48): 直接十六进制转十进制，表示当前实际开度百分比(0-100)
        readValveDateDto.setValveCurrentOpen(Integer.parseInt(hexString.substring(index,index+2),16));
        index = index+2;
        /* ========== 阀门控制参数解析 ========== */
        // 延迟控制时间间隔(索引98-105, 4字节, 字节位置49-52): 小端序转大端序后转十进制，单位为分钟
        Integer delayControlTimeInterval =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setDelayControlTimeInterval(delayControlTimeInterval.toString());
        index = index + 8;

        // 时长控制模式持续时间(索引106-113, 4字节, 字节位置53-56): 小端序转大端序后转十进制，表示时长控制模式的持续时间
        Integer durationControlModeDuration =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setDurationControlModeDuration(durationControlModeDuration.toString());
        index = index + 8;

        // 流量控制模式流量大小(索引114-121, 4字节, 字节位置57-60): 小端序转大端序后转十进制，表示流量控制模式的目标流量值
        Integer flowControlModeFlowSize =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setFlowControlModeFlowSize(flowControlModeFlowSize.toString());
        index = index + 8;

        // 周期开关阀时间间隔(索引122-129, 4字节, 字节位置61-64): 小端序转大端序后转十进制，表示轮询控制模式的时间间隔
        Integer periodicOnOffValveTimeInterval =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setPeriodicOnOffValveTimeInterval(periodicOnOffValveTimeInterval.toString());
        index = index + 8;

        // 周期开关阀持续时间(索引130-137, 4字节, 字节位置65-68): 小端序转大端序后转十进制，单位为分钟，表示每次开启的持续时间
        Integer periodicSwitchingValveTime =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setPeriodicSwitchingValveTime(periodicSwitchingValveTime.toString());
        index = index + 8;

        // 周期开关阀次数(索引138-141, 2字节, 字节位置69-70): 小端序转大端序后转十进制，表示轮询控制的循环次数
        Integer regularOnOffValveNumber =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readValveDateDto.setPeriodicSwitchingValveTime(regularOnOffValveNumber.toString());
        index = index + 4;
        /* ========== 流量和压力监测数据解析 ========== */
        // 当前流量(索引142-145, 2字节, 字节位置71-72): 小端序转大端序后转十进制，表示当前实时流量值
        Integer currentTraffic =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readValveDateDto.setCurrentTraffic(currentTraffic.toString());
        index = index + 4;

        // 阀门压力(索引146-149, 2字节, 字节位置73-74): 小端序转大端序后转十进制，表示阀门当前压力值
        Integer valvePressure =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readValveDateDto.setValvePressure(valvePressure.toString());
        index = index + 4;
        // 25.07.09 协议修改
        // 流速(索引150-153, 2字节, 字节位置75-76): 小端序转大端序后转十进制，表示流速
        Integer velocity =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readValveDateDto.setVelocity(velocity);
        index = index + 4;
        // 累计流量（索引154-161, 4字节, 字节位置77-80): 小端序转大端序后转十进制，表示累计流量
        Integer cumulativeTraffic =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setCumulativeTraffic(cumulativeTraffic);
        index = index + 8;

       /* *//* ========== 阀门异常控制状态解析 ========== *//*
        // 阀门异常控制使能状态(索引150-153, 2字节, 字节位置75-76): 小端序转大端序后转十进制，再转二进制进行位解析
        Integer status = Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);

        // 十进制转二进制字符串，用于按位解析异常状态
        String binaryString = Integer.toBinaryString(status);

        // 截取二进制字符串的后8位，确保位数一致性
        String binaryData = binaryString.substring(binaryString.length() - 8, binaryString.length());

        // 按位解析各种异常状态（每2位表示一种异常状态）
        String deviceGPS = binaryData.substring(0, 2);          // 位0-1: 设备GPS异常状态
        String deviceUndervoltage = binaryData.substring(2, 4); // 位2-3: 设备欠压异常状态
        String deviceOffline = binaryData.substring(4, 6);      // 位4-5: 设备离线异常状态
        String wirelessAbnormality = binaryData.substring(6, 8); // 位6-7: 无线通信异常状态

        // 创建异常控制状态对象并设置各异常状态值
        ValveAbnormalControlDIC valveAbnormalControlDIC = new ValveAbnormalControlDIC();
        valveAbnormalControlDIC.setDeviceGPS(deviceGPS);              // GPS异常状态
        valveAbnormalControlDIC.setDeviceUndervoltage(deviceUndervoltage); // 欠压异常状态
        valveAbnormalControlDIC.setDeviceOffline(deviceOffline);      // 离线异常状态
        valveAbnormalControlDIC.setWirelessAbnormality(wirelessAbnormality); // 无线异常状态

        readValveDateDto.setValveAbnormalStatus(valveAbnormalControlDIC);
        index = index + 4;
        *//* ========== 阀门自检和开度限制参数解析 ========== *//*
        // 阀门开机自检状态(索引154-155, 1字节, 字节位置77-77): 直接十六进制转十进制，表示开机自检结果状态
        Integer  valveStartupSelfTestStatus= Integer.parseInt(hexString.substring(index,index+2),16);
        readValveDateDto.setValveStartupSelfTestStatus(valveStartupSelfTestStatus.toString());
        index = index+ 2;

        // 异常预设开度(索引156-157, 1字节, 字节位置78-78): 直接十六进制转十进制，表示异常情况下的预设开度百分比
        Integer  abnormalPresetOpen= Integer.parseInt(hexString.substring(index,index+2),16);
        readValveDateDto.setAbnormalPresetOpen(abnormalPresetOpen.toString());
        index = index+2;
*/
        /*// 阀门开度上限(索引158-159, 1字节, 字节位置79-79): 直接十六进制转十进制，表示阀门开度的最大限制值
        Integer  valveOpenUpper = Integer.parseInt(hexString.substring(index,index+2),16);
        readValveDateDto.setValveOpenUpper(valveOpenUpper.toString());
        index = index+2;

        // 阀门开度下限(索引160-161, 1字节, 字节位置80-80): 直接十六进制转十进制，表示阀门开度的最小限制值
        Integer  valveOpenLower = Integer.parseInt(hexString.substring(index,index+2),16);
        readValveDateDto.setValveOpenLower(valveOpenLower.toString());
        index = index+2;*/
        /* ========== 阀门累积统计数据解析 ========== */
        // 阀门累积工作时间(索引162-169, 4字节, 字节位置81-84): 小端序转大端序后转十进制，单位为分钟，表示阀门总工作时长
        Integer valveAccumulateWorkTime =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setValveAccumulateWorkTime(valveAccumulateWorkTime.toString());
        index = index+8;

        // 阀门累积开启时间(索引170-177, 4字节, 字节位置85-88): 小端序转大端序后转十进制，单位为分钟，表示阀门累计开启时长
        Integer valveAccumulateOpenTime =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setValveAccumulateOpenTime(valveAccumulateOpenTime.toString());
        index = index+8;

        // 阀门累计动作次数(索引178-185, 4字节, 字节位置89-92): 小端序转大端序后转十进制，表示阀门开关动作的累计次数
        Integer valveAccumulateActionsNumber =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16);
        readValveDateDto.setValveAccumulateActionsNumber(valveAccumulateActionsNumber.toString());
        index = index+8;

        /* ========== 电源管理数据解析 ========== */
        // 太阳能充电电流(索引186-189, 2字节, 字节位置93-94): 直接十六进制转十进制，单位为毫安(mA)，表示太阳能板充电电流
        Integer solarChargCurrent = Integer.parseInt(hexString.substring(index, index + 4),16);
        readValveDateDto.setSolarChargCurrent(solarChargCurrent.toString());
        index = index+4;

        // 压力传感器电压值(索引190-193, 2字节, 字节位置95-96): 协议预留字段，直接十六进制转十进制
        Integer pressureSensorVoltage =  Integer.parseInt(hexString.substring(index, index + 4),16);
        readValveDateDto.setPressureSensorVoltage(pressureSensorVoltage.toString());
        index = index+4;

        // 压力传感器电流值(索引194-197, 2字节, 字节位置97-98): 协议预留字段，直接十六进制转十进制
        Integer pressureSensorCurrent  =  Integer.parseInt(hexString.substring(index, index + 4),16);
        readValveDateDto.setPressureSensorCurrent(pressureSensorCurrent.toString());
        index = index+4;

        // 保留字段2(索引198-201, 2字节, 字节位置99-100): 协议预留字段，直接十六进制转十进制
        Integer reserve =  Integer.parseInt(hexString.substring(index, index + 4),16);
        readValveDateDto.setReserve(reserve.toString());
        index = index+4;


        /* ========== 数据包尾部字段解析 ========== */
        // SER序列号(索引202-203, 1字节, 字节位置101-101): 直接提取十六进制字符串，表示序列号或数据包标识符
        readValveDateDto.setSer(hexString.substring(index, index + 2));
        index = index+2;

        // 校验位(索引204-205, 1字节, 字节位置102-102): 直接提取十六进制字符串，用于数据完整性校验
        readValveDateDto.setChecksum(hexString.substring(index, index + 2));
        index = index+2;

        // 结束符(索引206-207, 1字节, 字节位置103-103): 直接提取十六进制字符串，表示数据包结束标识
        readValveDateDto.setEnd(hexString.substring(index, index + 2));

        // 返回完整解析后的阀控数据对象
        return readValveDateDto;
    }


    public static void main(String[] args) {
        String a = "68010000258668 85 5C0086902E310F1107197F3FC61B030000185802240F250710000065000000006400ED1300026464640000000000000000000000000000000000000000000000000000000000000000AE000000AC000000240000008C004A0286010000C12A16";
        ReadValveDateDto readValveDateDto = readValveDateParse(a);
        System.out.println(readValveDateDto);
    }
    //重置阀门运行时间
    public static ResetValveDateDto resetValveDateParse(String hexString){
        //打印日志
        log.info("重置阀门运行时间传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != RESETVALVE_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建阀控读取的实体类
        ResetValveDateDto resetValveDateDto = new ResetValveDateDto();

        //包头(7 bytes)
        resetValveDateDto.setHeader(hexString.substring(0,14));

        //设置下标
        int index = 14;

        //控制码 (1 byte)
        resetValveDateDto.setControlCode(hexString.substring(index,index+2));
        //
        index = index+2;
        //
        Integer controlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        resetValveDateDto.setDataLength(controlCode.toString());
        //
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (controlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //协议标识
        resetValveDateDto.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index = index+4;
        //状态字
        resetValveDateDto.setStatusWord(hexString.substring(index,index+2));
        //
        index = index+2;
        //ser
        resetValveDateDto.setSer(hexString.substring(index,index+2));
        //
        index = index + 2;
        //
        resetValveDateDto.setChecksum(hexString.substring(index,index+2));
        //
        index = index + 2;
        //判断结尾符是否正确
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        resetValveDateDto.setEnd(end);

        return resetValveDateDto;
    }

    //读取阀门开机自检
    public static ReadValveOpenOnSelfTestDto readValveOpenOnSelfTestParse(String hexString){
        //打印日志
        log.info("读取阀门开机自检传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != READPOWERONSELFTEST_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建阀控读取的实体类
        ReadValveOpenOnSelfTestDto readValveOpenOnSelfTestDto = new ReadValveOpenOnSelfTestDto();

        //包头(7 bytes)
        readValveOpenOnSelfTestDto.setHeader(hexString.substring(0,14));

        //设置下标
        int index = 14;

        //控制码 (1 byte)
        readValveOpenOnSelfTestDto.setControlCode(hexString.substring(index,index+2));
        //
        index = index+2;
        //
        Integer controlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        readValveOpenOnSelfTestDto.setDataLength(controlCode.toString());
        //
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (controlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //协议标识
        readValveOpenOnSelfTestDto.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index += 4;
        ///自检状态(0:未完成自检，1：自检进行中，2：自检完成) 占1个字节
        Integer selfCheckStatus = Integer.parseInt(hexString.substring(index, index + 2), 16);
        readValveOpenOnSelfTestDto.setSelfCheckStatus(selfCheckStatus.toString());
        //
        index += 2;
        //
        Integer powerOnSelfTest = Integer.parseInt(hexString.substring(index, index + 2), 16);
        readValveOpenOnSelfTestDto.setPowerOnSelfTest(powerOnSelfTest.toString());
        //
        index += 2;
        //ser
        readValveOpenOnSelfTestDto.setSer(hexString.substring(index,index+2));
        //
        index = index + 2;
        //
        readValveOpenOnSelfTestDto.setChecksum(hexString.substring(index,index+2));
        //
        index = index + 2;
        //判断结尾符是否正确
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        readValveOpenOnSelfTestDto.setEnd(end);

        return readValveOpenOnSelfTestDto;
    }

    //设置阀门开机自检
    public static SetUpValveOpenOnSelfTestDTO setUpValveOpenOnSelfTestParse(String hexString){
        //打印日志
        log.info("设置阀门开机自检传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != SETUPPOWERONSELFTEST_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建阀控读取的实体类
        SetUpValveOpenOnSelfTestDTO setUpValveOpenOnSelfTestDTO = new SetUpValveOpenOnSelfTestDTO();

        //包头(7 bytes)
        setUpValveOpenOnSelfTestDTO.setHeader(hexString.substring(0,14));

        //设置下标
        int index = 14;

        //控制码 (1 byte)
        setUpValveOpenOnSelfTestDTO.setControlCode(hexString.substring(index,index+2));
        //
        index = index+2;
        //
        Integer controlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        setUpValveOpenOnSelfTestDTO.setDataLength(controlCode.toString());
        //
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (controlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //协议标识
        setUpValveOpenOnSelfTestDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index += 4;
        //状态字
        setUpValveOpenOnSelfTestDTO.setStatusWord(hexString.substring(index,index+2));
        //
        index += 2;
        //ser
        setUpValveOpenOnSelfTestDTO.setSer(hexString.substring(index,index+2));
        //
        index = index + 2;
        //
        setUpValveOpenOnSelfTestDTO.setChecksum(hexString.substring(index,index+2));
        //
        index = index + 2;
        //判断结尾符是否正确
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        setUpValveOpenOnSelfTestDTO.setEnd(end);

        return setUpValveOpenOnSelfTestDTO;
    }

    //读取阀门异常控制
    public static ReadValveAbnormalControlDTO readValveAbnormalControlParse(String hexString){
        //打印日志
        log.info("读取阀门异常控制传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != READVALVEABNORMALCONTROL_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建阀控读取的实体类
        ReadValveAbnormalControlDTO readValveAbnormalControlDTO = new ReadValveAbnormalControlDTO();

        //包头(7 bytes)
        readValveAbnormalControlDTO.setHeader(hexString.substring(0,14));

        //设置下标
        int index = 14;

        //控制码 (1 byte)
        readValveAbnormalControlDTO.setControlCode(hexString.substring(index,index+2));
        //
        index = index+2;
        //
        Integer controlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        readValveAbnormalControlDTO.setDataLength(controlCode.toString());
        //
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (controlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //协议标识
        readValveAbnormalControlDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index += 4;
        //
        //阀门异常控制使能状态 小端值转大端值  再 16 转10
        Integer status = Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        // 10转成2进制
        String binaryString = Integer.toBinaryString(status);
        //截取2进制后八位
        String binaryData = binaryString.substring(binaryString.length() - 8, binaryString.length());
        //设备gps值
        String deviceGPS = binaryData.substring(0, 2);
        //设备欠压值
        String deviceUndervoltage = binaryData.substring(2, 4);
        //设备离线
        String deviceOffline = binaryData.substring(4, 6);
        //无线异常值
        String wirelessAbnormality = binaryData.substring(6, 8);
        //设备gps
        readValveAbnormalControlDTO.getValveAbnormalStatus().setDeviceGPS(deviceGPS);
        //设备欠压
        readValveAbnormalControlDTO.getValveAbnormalStatus().setDeviceUndervoltage(deviceUndervoltage);
        //设备离线
        readValveAbnormalControlDTO.getValveAbnormalStatus().setDeviceOffline(deviceOffline);
        //无线异常
        readValveAbnormalControlDTO.getValveAbnormalStatus().setDeviceUndervoltage(wirelessAbnormality);
        //
        index += 4;
        //
        //异常预设开度 16转10
        Integer  abnormalPresetOpen= Integer.parseInt(hexString.substring(index,index+2),16);
        readValveAbnormalControlDTO.setAbnormalPresetOpen(abnormalPresetOpen.toString());
        //
        index += 2;
        //ser
        readValveAbnormalControlDTO.setSer(hexString.substring(index,index+2));
        //
        index = index + 2;
        //
        readValveAbnormalControlDTO.setChecksum(hexString.substring(index,index+2));
        //
        index = index + 2;
        //判断结尾符是否正确
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        readValveAbnormalControlDTO.setEnd(end);

        return readValveAbnormalControlDTO;
    }

    //设置阀门异常控制
    public static SetUpValveAbnormalControlDTO setUpValveAbnormalControlParse(String hexString){
        //打印日志
        log.info("设置阀门异常控制传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != SETUPVALVEABNORMALCONTROL_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建阀控读取的实体类
        SetUpValveAbnormalControlDTO setUpValveAbnormalControlDTO = new SetUpValveAbnormalControlDTO();

        //包头(7 bytes)
        setUpValveAbnormalControlDTO.setHeader(hexString.substring(0,14));

        //设置下标
        int index = 14;

        //控制码 (1 byte)
        setUpValveAbnormalControlDTO.setControlCode(hexString.substring(index,index+2));
        //
        index = index+2;
        //
        Integer controlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        setUpValveAbnormalControlDTO.setDataLength(controlCode.toString());
        //
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (controlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //协议标识
        setUpValveAbnormalControlDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index += 4;

        //状态字
        Integer statusWord =  Integer.parseInt(hexString.substring(index,index+2),16);
        setUpValveAbnormalControlDTO.setStatusWord(statusWord.toString());
        //
        index = index + 2;
        //ser
        setUpValveAbnormalControlDTO.setSer(hexString.substring(index,index+2));
        //
        index = index + 2;
        //
        setUpValveAbnormalControlDTO.setChecksum(hexString.substring(index,index+2));
        //
        index = index + 2;
        //判断结尾符是否正确
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        setUpValveAbnormalControlDTO.setEnd(end);

        return setUpValveAbnormalControlDTO;
    }

    //读取阀门上下参数
    public static ReadValveUpAndDownParamterDTO readValveUpAndDownParamterParse(String hexString) {
        //打印日志
        log.info("读取阀门上下参数读取传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != READVALVEUPANDDOWNPARAMTER_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建设备读取的实体类
        ReadValveUpAndDownParamterDTO readValveUpAndDownParamterDTO = new ReadValveUpAndDownParamterDTO();
        // 包头 (7 bytes)
        readValveUpAndDownParamterDTO.setHeader(hexString.substring(0,14));
        //设置下标
        int index = 14;
        //控制码 (1 byte)
        readValveUpAndDownParamterDTO.setControlCode(hexString.substring(index,index+2));
        //下标+2
        index = index+2;
        //数据长度 (2 byte)（数据小端值转成大端值）然后16进制转10进制
        Integer ControlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        readValveUpAndDownParamterDTO.setDataLength(ControlCode.toString());

        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (ControlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //数据标识 (2 bytes)
        readValveUpAndDownParamterDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index=index+4;
        //阀门开度上限
        Integer valveOpenUpper = Integer.parseInt(hexString.substring(index,index+2),16);
        readValveUpAndDownParamterDTO.setValveOpenUpper(valveOpenUpper.toString());
        //
        index=index+2;
        //阀门开度下限
        Integer valveOpenLower = Integer.parseInt(hexString.substring(index,index+2),16);
        readValveUpAndDownParamterDTO.setValveOpenLower(valveOpenLower.toString());
        //
        index=index+2;
        //SER，序列号或标识符
        String ser = hexString.substring(index, index + 2);
        readValveUpAndDownParamterDTO.setSer(ser);
        //
        index = index +2;
        //
        String checksum =  hexString.substring(index, index + 2);
        readValveUpAndDownParamterDTO.setChecksum(checksum);
        //
        index = index +2;
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        readValveUpAndDownParamterDTO.setEnd(end);

        return readValveUpAndDownParamterDTO;

    }

    //设置阀门上下参数
    public static SetUpValveUpAndDownParamterDTO setUpValveUpAndDownParamterParse(String hexString) {
        //打印日志
        log.info("设置阀门上下参数读取传来的数据: {}", hexString);
        //判断字符串长度是否符合标准长度
        if (hexString.length() != SETUPVALVEUPANDDOWNPARAMTER_LENGTH*2){
            throw new IllegalArgumentException("数据长度不正确！");
        }
        //创建设备读取的实体类
        SetUpValveUpAndDownParamterDTO setUpValveUpAndDownParamterDTO = new SetUpValveUpAndDownParamterDTO();
        // 包头 (7 bytes)
        setUpValveUpAndDownParamterDTO.setHeader(hexString.substring(0,14));
        //设置下标
        int index = 14;
        //控制码 (1 byte)
        setUpValveUpAndDownParamterDTO.setControlCode(hexString.substring(index,index+2));
        //下标+2
        index = index+2;
        //数据长度 (1 byte)（数据小端值转成大端值）然后16进制转10进制
        Integer ControlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        setUpValveUpAndDownParamterDTO.setDataLength(ControlCode.toString());

        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (ControlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //数据标识 (2 bytes)
        setUpValveUpAndDownParamterDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index=index+4;
        //状态字
        Integer statusWord =  Integer.parseInt(hexString.substring(index,index+2),16);
        setUpValveUpAndDownParamterDTO.setStatusWord(statusWord.toString());
        //
        index = index + 2;

        //SER，序列号或标识符
        String ser = hexString.substring(index, index + 2);
        setUpValveUpAndDownParamterDTO.setSer(ser);
        //
        index = index +2;
        //
        String checksum =  hexString.substring(index, index + 2);
        setUpValveUpAndDownParamterDTO.setChecksum(checksum);
        //
        index = index +2;
        if(!hexString.substring(index, index + 2).equals("16")){
            throw new IllegalArgumentException("数据结尾符不正确");
        }
        //结束符
        String end =  hexString.substring(index, index + 2);
        setUpValveUpAndDownParamterDTO.setEnd(end);

        return setUpValveUpAndDownParamterDTO;

    }

    /**
     * 读取温室传感器数据解析方法
     * <p>
     * 解析温室传感器控制器上报的多传感器数据，支持多种传感器类型的数据解析。
     * 数据包采用小端序和大端序混合编码，部分字段需要进行字节序转换。
     * 支持动态数量的传感器数据解析，每个传感器可包含多个数据项。
     * </p>
     *
     * <h3>数据包结构说明（变长数据包）：</h3>
     * <table border="1">
     * <tr><th>字段名称</th><th>索引位置</th><th>字节长度</th><th>数据格式</th><th>业务含义</th></tr>
     * <tr><td>包头</td><td>0-13 (字节位置0-6)</td><td>7字节</td><td>HEX字符串</td><td>数据包起始标识</td></tr>
     * <tr><td>控制码</td><td>14-15 (字节位置7-7)</td><td>1字节</td><td>HEX字符串</td><td>命令控制码</td></tr>
     * <tr><td>数据长度</td><td>16-19 (字节位置8-9)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>数据域长度</td></tr>
     * <tr><td>协议标识</td><td>20-23 (字节位置10-11)</td><td>2字节</td><td>小端序HEX→大端序</td><td>协议版本标识(9003)</td></tr>
     * <tr><td>时间戳</td><td>24-35 (字节位置12-17)</td><td>6字节</td><td>HEX(秒分时日月年)</td><td>设备时间</td></tr>
     * <tr><td>网络状态</td><td>36-37 (字节位置18-18)</td><td>1字节</td><td>小端序HEX→大端序→十进制</td><td>网络连接状态</td></tr>
     * <tr><td>信号强度</td><td>38-39 (字节位置19-19)</td><td>1字节</td><td>HEX→十进制</td><td>信号强度值</td></tr>
     * <tr><td>在线时长</td><td>40-47 (字节位置20-23)</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>网络在线时长(秒)</td></tr>
     * <tr><td>设备状态</td><td>48-51 (字节位置24-25)</td><td>2字节</td><td>HEX→十进制</td><td>设备运行状态</td></tr>
     * <tr><td>上传间隔</td><td>52-55 (字节位置26-27)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>主动上传时间间隔(秒)</td></tr>
     * <tr><td>电源电压</td><td>56-59 (字节位置28-29)</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>电源电压值(mV)</td></tr>
     * <tr><td>软件版本</td><td>60-67 (字节位置30-33)</td><td>4字节</td><td>HEX→十进制(分段解析)</td><td>软件版本号</td></tr>
     * <tr><td>硬件版本</td><td>68-75 (字节位置34-37)</td><td>4字节</td><td>HEX→十进制(分段解析)</td><td>硬件版本号</td></tr>
     * <tr><td>传感器总数</td><td>76-77 (字节位置38-38)</td><td>1字节</td><td>HEX→十进制</td><td>设备连接的传感器总数</td></tr>
     * <tr><td>本包传感器数</td><td>78-79 (字节位置39-39)</td><td>1字节</td><td>HEX→十进制</td><td>当前数据包包含的传感器数量</td></tr>
     * <tr><td>保留字段</td><td>80-95 (字节位置40-47)</td><td>8字节</td><td>HEX字符串</td><td>预留字段</td></tr>
     * </table>
     *
     * <h3>传感器数据结构（动态重复）：</h3>
     * <table border="1">
     * <tr><th>字段名称</th><th>字节长度</th><th>数据格式</th><th>业务含义</th></tr>
     * <tr><td>传感器序号</td><td>1字节</td><td>HEX字符串</td><td>传感器编号标识</td></tr>
     * <tr><td>数据个数</td><td>1字节</td><td>HEX→十进制</td><td>该传感器包含的数据项数量</td></tr>
     * <tr><td>传感器型号</td><td>2字节</td><td>小端序HEX→大端序→十进制</td><td>传感器型号编码(1-29)</td></tr>
     * <tr><td colspan="4"><strong>数据项结构（按数据个数重复）：</strong></td></tr>
     * <tr><td>数据类型</td><td>1字节</td><td>HEX→十进制</td><td>数据类型编码(1-31)</td></tr>
     * <tr><td>传感器状态</td><td>1字节</td><td>HEX→十进制</td><td>传感器工作状态(0-7)</td></tr>
     * <tr><td>数据值</td><td>4字节</td><td>小端序HEX→大端序→十进制</td><td>传感器数据值</td></tr>
     * </table>
     *
     * <h3>数据包尾部字段：</h3>
     * <table border="1">
     * <tr><th>字段名称</th><th>字节长度</th><th>数据格式</th><th>业务含义</th></tr>
     * <tr><td>序列号</td><td>1字节</td><td>HEX字符串</td><td>SER序列号标识符</td></tr>
     * <tr><td>校验位</td><td>1字节</td><td>HEX字符串</td><td>数据校验位</td></tr>
     * <tr><td>结束符</td><td>1字节</td><td>HEX字符串</td><td>数据包结束标识</td></tr>
     * </table>
     *
     * <h3>支持的传感器型号（1-29）：</h3>
     * <ul>
     * <li><strong>1</strong>: 卡片式水表</li>
     * <li><strong>2-6</strong>: 中天系列传感器（光照、湿度、温度、风向、风速）</li>
     * <li><strong>7-9</strong>: 中天多深度温湿度传感器（20cm、40cm、60cm）</li>
     * <li><strong>10-13</strong>: 环境监测传感器（百叶箱、风速、风向、液位）</li>
     * <li><strong>14-16</strong>: 多深度温湿度传感器（20cm、40cm、60cm）</li>
     * <li><strong>17-23</strong>: 土壤监测传感器（PH、电导率、多深度温湿度）</li>
     * <li><strong>24-29</strong>: 专用传感器（点阵屏、雨量、物联传感器、紫外线、光合辐射）</li>
     * </ul>
     *
     * <h3>支持的数据类型（1-31）：</h3>
     * <ul>
     * <li><strong>1-9</strong>: 环境参数（光照度、空气温湿度、风速风向、雨量、气压、CO2、紫外线）</li>
     * <li><strong>10-21</strong>: 土壤参数（多深度土壤温湿度10-60cm）</li>
     * <li><strong>22-31</strong>: 特殊参数（土壤电导率、PH值、蒸发量、露点、辐射、噪音、PM2.5/PM10、液位）</li>
     * </ul>
     *
     * <h3>传感器状态编码（0-7）：</h3>
     * <ul>
     * <li><strong>0</strong>: 通讯状态</li>
     * <li><strong>1</strong>: 故障状态</li>
     * <li><strong>2</strong>: 预警状态</li>
     * <li><strong>3-4</strong>: 保留</li>
     * <li><strong>5</strong>: 数据故障状态</li>
     * <li><strong>6</strong>: 状态故障状态</li>
     * <li><strong>7</strong>: 通讯故障状态</li>
     * </ul>
     *
     * <h3>特殊处理逻辑：</h3>
     * <ul>
     * <li><strong>版本号解析</strong>：软件版本和硬件版本均为4字节分别转换为十进制，用"-"连接</li>
     * <li><strong>字节序转换</strong>：使用CommonUtil.reverse()进行小端序到大端序转换</li>
     * <li><strong>时间解析</strong>：调用parseDateTime()方法解析6字节时间戳</li>
     * <li><strong>数据值处理</strong>：除特定类型外，数据值除以100并保留两位小数</li>
     * <li><strong>特殊数据类型</strong>：光照度、CO2浓度、紫外线强度、土壤电导率、辐射、噪音、PM值不进行除法处理</li>
     * <li><strong>动态解析</strong>：根据传感器数量和数据个数动态解析变长数据包</li>
     * <li><strong>数据长度校验</strong>：验证数据长度与报文指定长度的一致性</li>
     * </ul>
     *
     * @param hexString 十六进制字符串，变长数据包，最小长度为基础字段+尾部字段
     * @return ReadGreenhouseSensorsDTO 解析后的温室传感器数据传输对象，包含设备信息和多传感器数据
     * @throws IllegalArgumentException 当数据长度不正确或数据长度与报文指定长度不匹配时抛出
     * @throws NullPointerException 当传感器数据类型解析失败导致dataType为null时抛出
     * @see CommonUtil#reverse(String) 字节序转换工具方法
     * @see #parseDateTime(String) 时间解析方法
     * @see ReadGreenhouseSensorsDTO 温室传感器数据传输对象
     * @see MultipleSensors 多传感器数据对象
     * @see MultipleData 传感器数据项对象
     * @since 1.0
     * <AUTHOR>
     */
    public static ReadGreenhouseSensorsDTO readGreenhouseSensorsParse(String hexString) {
        /* ========== 方法初始化和日志记录 ========== */
        // 记录接收到的原始十六进制数据，便于调试和问题追踪
        log.info("读取温室传感器数据传来的数据: {}", hexString);

        // 创建温室传感器数据传输对象，用于存储解析后的所有数据
        ReadGreenhouseSensorsDTO readGreenhouseSensorsDTO = new ReadGreenhouseSensorsDTO();

        /* ========== 数据包头部解析 ========== */
        // 包头(索引0-13, 7字节, 字节位置0-6): 数据包起始标识，直接提取十六进制字符串
        readGreenhouseSensorsDTO.setHeader(hexString.substring(0,14));

        // 初始化解析索引指针，从包头后开始
        int index = 14;

        // 控制码(索引14-15, 1字节, 字节位置7-7): 命令控制码，直接提取十六进制字符串
        readGreenhouseSensorsDTO.setControlCode(hexString.substring(index,index+2));
        index = index+2;

        // 数据长度(索引16-19, 2字节, 字节位置8-9): 小端序转大端序后转十进制，表示数据域字节数
        Integer ControlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        readGreenhouseSensorsDTO.setDataLength(ControlCode.toString());

        // 数据长度一致性校验：去掉包头、控制码、数据长度、校验位、结束符共12字节(24个字符)
        int actualLength = hexString.length() - 24;
        // 数据长度字段值*2应等于实际数据域字符串长度
        if (ControlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        index = index+4;

        // 协议标识(索引20-23, 2字节, 字节位置10-11): 小端序转大端序，标识协议版本(应为9003)
        readGreenhouseSensorsDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        index=index+4;

        /* ========== 时间戳解析 ========== */
        // 时间戳(索引24-35, 6字节, 字节位置12-17): 按秒分时日月年顺序排列的BCD编码时间
        String hexDateTime = hexString.substring(index, index + 12);
        readGreenhouseSensorsDTO.setDateTimeHex(hexDateTime);  // 保存原始十六进制时间
        readGreenhouseSensorsDTO.setDateTime(parseDateTime(hexDateTime));  // 解析为可读时间格式
        index = index+12;

        /* ========== 网络状态信息解析 ========== */
        // 网络状态(索引36-37, 1字节, 字节位置18-18): 小端序转大端序后转十进制，表示网络连接状态
        readGreenhouseSensorsDTO.setNetworkStatus(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+2)),16));
        index = index+2;

        // 信号强度(索引38-39, 1字节, 字节位置19-19): 直接十六进制转十进制，表示信号强度值
        readGreenhouseSensorsDTO.setSignalStrength(Integer.parseInt(hexString.substring(index,index+2),16));
        index = index+2;

        // 网络在线时长(索引40-47, 4字节, 字节位置20-23): 小端序转大端序后转十进制，单位为秒
        readGreenhouseSensorsDTO.setOnlineDuration(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16));
        index = index+8;

        /* ========== 设备状态信息解析 ========== */
        // 设备运行状态(索引48-51, 2字节, 字节位置24-25): 直接十六进制转十进制，表示设备当前运行状态
        readGreenhouseSensorsDTO.setDeviceStatus(Integer.parseInt(hexString.substring(index,index+4),16));
        index = index+4;

        // 主动上传时间间隔(索引52-55, 2字节, 字节位置26-27): 小端序转大端序后转十进制，单位为秒
        readGreenhouseSensorsDTO.setUploadInterval(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16));
        index = index+4;
        /* ========== 电源电压解析 ========== */
        // 电源电压(索引56-59, 2字节, 字节位置28-29): 小端序转大端序后转十进制，单位为毫伏(mV)
        double powerVoltage =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readGreenhouseSensorsDTO.setPowerVoltage(powerVoltage);
        index = index+4;

        /* ========== 版本信息解析 ========== */
        // 软件版本(索引60-67, 4字节, 字节位置30-33): 每字节分别转十进制，用"-"连接组成版本号
        String softwareVersion = hexString.substring(index,index+8);
        Integer softwareVersion1 = Integer.parseInt(softwareVersion.substring(0, 2), 16);  // 主版本号
        Integer softwareVersion2 = Integer.parseInt(softwareVersion.substring(2, 4), 16);  // 次版本号
        Integer softwareVersion3 = Integer.parseInt(softwareVersion.substring(4, 6), 16);  // 修订版本号
        Integer softwareVersion4 = Integer.parseInt(softwareVersion.substring(6, 8), 16);  // 构建版本号
        readGreenhouseSensorsDTO.setSoftwareVersion(softwareVersion1+"-"+softwareVersion2+"-"+softwareVersion3+"-"+softwareVersion4);
        index = index+8;

        // 硬件版本(索引68-75, 4字节, 字节位置34-37): 每字节分别转十进制，用"-"连接组成版本号
        String version = hexString.substring(index,index+8);
        Integer version1 = Integer.parseInt(version.substring(0, 2), 16);  // 主版本号
        Integer version2 = Integer.parseInt(version.substring(2, 4), 16);  // 次版本号
        Integer version3 = Integer.parseInt(version.substring(4, 6), 16);  // 修订版本号
        Integer version4 = Integer.parseInt(version.substring(6, 8), 16);  // 构建版本号
        readGreenhouseSensorsDTO.setHardwareVersion(version1+"-"+version2+"-"+version3+"-"+version4);
        index = index+8;
        /* ========== 传感器数量信息解析 ========== */
        // 传感器总数(索引76-77, 1字节, 字节位置38-38): 直接十六进制转十进制，表示设备连接的传感器总数
        readGreenhouseSensorsDTO.setSensorsTotalNumber(Integer.parseInt(hexString.substring(index,index+2),16));
        index = index+2;

        // 本包传感器总数(索引78-79, 1字节, 字节位置39-39): 直接十六进制转十进制，表示当前数据包包含的传感器数量
        Integer thisPackageNumber = Integer.parseInt(hexString.substring(index,index+2),16);
        readGreenhouseSensorsDTO.setThisPackagesensorsTotalNumber(thisPackageNumber);
        index = index+2;
        //todo 服务器1网络状态字段(索引80-82, 1字节, 字节位置40-40): 表示服务器1网络状态
        String server1NetworkStatusHex = hexString.substring(index,index+2);
         readGreenhouseSensorsDTO.setServer1NetworkStatus(Integer.parseInt(server1NetworkStatusHex,16));
          index = index+2;
        // todo 服务器2网络状态字段(索引83-85, 1字节, 字节位置41-41): 表示服务器2网络状态
        String server2NetworkStatusHex = hexString.substring(index,index+2);
        readGreenhouseSensorsDTO.setServer2NetworkStatus(Integer.parseInt(server2NetworkStatusHex,16));
        index = index+2;
        // 保留字段(索引86-95, 6字节, 字节位置42-47): 协议预留字段，直接提取十六进制字符串,保留字段不解析
        /*String reserved = hexString.substring(index, index + 12);
        Integer reserve =  Integer.parseInt(reserved);
        readGreenhouseSensorsDTO.setReserved(reserve.toString());*/
        index = index+12;
        /* ========== 动态传感器数据解析 ========== */
        // 根据本包传感器数量进行动态解析，如果为0则跳过传感器数据解析
        if (thisPackageNumber != 0){
            // 创建传感器列表，用于存储所有传感器的数据
            List<MultipleSensors> sensorsList = new ArrayList<>();

            // 循环解析每个传感器的数据
            for (int i = 1; i <= thisPackageNumber; i++) {
                // 创建当前传感器的数据项列表
                List<MultipleData> dataList = new ArrayList<>();

                // 创建传感器对象
                MultipleSensors multipleSensors = new MultipleSensors();

                // 传感器序号(1字节): 传感器编号标识，直接提取十六进制字符串
                multipleSensors.setSerialNumber(hexString.substring(index, index + 2));
                index+=2;

                // 数据个数(1字节): 该传感器包含的数据项数量，十六进制转十进制
                Integer dataNum = Integer.parseInt(hexString.substring(index, index + 2),16);
                index+=2;
                multipleSensors.setDataNumber(dataNum);

                // 传感器型号(2字节): 小端序转大端序后转十进制，用于识别传感器类型(1-29)
                Integer model = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)),16);

                /* ========== 传感器型号映射解析 ========== */
                // 根据型号编码设置传感器型号名称，支持29种不同类型的传感器
                switch (model){
                    case 1: multipleSensors.setSensorModel("卡片式水表");break;
                    case 2: multipleSensors.setSensorModel("中天光照传感器");break;
                    case 3: multipleSensors.setSensorModel("中天湿度传感器");break;
                    case 4: multipleSensors.setSensorModel("中天温度传感器");break;
                    case 5: multipleSensors.setSensorModel("中天风向传感器");break;
                    case 6: multipleSensors.setSensorModel("中天风速传感器");break;
                    case 7: multipleSensors.setSensorModel("中天20cm温湿度传感器");break;
                    case 8: multipleSensors.setSensorModel("中天40cm温湿度传感器");break;
                    case 9: multipleSensors.setSensorModel("中天60cm温湿度传感器");break;
                    case 10: multipleSensors.setSensorModel("百叶箱传感器");break;
                    case 11: multipleSensors.setSensorModel("风速传感器");break;
                    case 12: multipleSensors.setSensorModel("风向传感器");break;
                    case 13: multipleSensors.setSensorModel("液位传感器");break;
                    case 14: multipleSensors.setSensorModel("20cm温湿度传感器");break;
                    case 15: multipleSensors.setSensorModel("40cm温湿度传感器");break;
                    case 16: multipleSensors.setSensorModel("60cm温湿度传感器");break;
                    case 17: multipleSensors.setSensorModel("土壤PH传感器");break;
                    case 18: multipleSensors.setSensorModel("土壤电导率传感器");break;
                    case 19: multipleSensors.setSensorModel("中天土壤PH传感器");break;
                    case 20: multipleSensors.setSensorModel("中天雨量传感器");break;
                    case 21: multipleSensors.setSensorModel("10cm温湿度传感器");break;
                    case 22: multipleSensors.setSensorModel("30cm温湿度传感器");break;
                    case 23: multipleSensors.setSensorModel("50cm温湿度传感器");break;
                    case 24: multipleSensors.setSensorModel("点阵屏");break;
                    case 25: multipleSensors.setSensorModel("雨量传感器");break;
                    case 26: multipleSensors.setSensorModel("通用物联传感器");break;
                    case 27: multipleSensors.setSensorModel("物联传感器");break;
                    case 28: multipleSensors.setSensorModel("中天紫外线传感器");break;
                    case 29: multipleSensors.setSensorModel("中天光合辐射传感器");break;
                    case 30: multipleSensors.setSensorModel("中天氮磷钾传感器");break;
                    case 31: multipleSensors.setSensorModel("光合有效辐射传感器");break;
                    case 32: multipleSensors.setSensorModel("建大仁科土壤张力传感器(15cm)");break;
                    case 33: multipleSensors.setSensorModel("建大仁科土壤张力传感器(30cm)");break;
                    case 34: multipleSensors.setSensorModel("建大仁科土壤张力传感器(45cm)");break;
                    case 35: multipleSensors.setSensorModel("气象传感器");break;
                    default: multipleSensors.setSensorModel("");
                }
                index+=4;

                /* ========== 传感器数据项解析循环 ========== */
                // 根据数据个数循环解析每个数据项，每个数据项包含类型、状态和数值
                for (int j = 1; j <= dataNum; j++) {
                    // 创建数据项对象
                    MultipleData  multipleData = new MultipleData();

                    // 数据类型(1字节): 直接十六进制转十进制，用于识别数据类型(1-31)
                    Integer dataType = Integer.parseInt(hexString.substring(index, index + 2),16);

                    /* ========== 数据类型映射解析 ========== */
                    // 根据数据类型编码设置数据类型名称和单位，支持31种不同类型的数据
                    switch (dataType){
                        case 1: multipleData.setDataType("光照度");multipleData.setUnit("Lux");break;
                        case 2: multipleData.setDataType("空气温度");multipleData.setUnit("°c");break;
                        case 3: multipleData.setDataType("空气湿度");multipleData.setUnit("%");break;
                        case 4: multipleData.setDataType("风速");multipleData.setUnit("M/s");break;
                        case 5: multipleData.setDataType("风向");multipleData.setUnit("");break;
                        case 6: multipleData.setDataType("雨量");multipleData.setUnit("Ms");break;
                        case 7: multipleData.setDataType("气压值");multipleData.setUnit("Pa");break;
                        case 8: multipleData.setDataType("C02浓度");multipleData.setUnit("mg/m3");break;
                        case 9: multipleData.setDataType("紫外线强度");multipleData.setUnit("W/m2");break;
                        case 10: multipleData.setDataType("10cm土壤温度");multipleData.setUnit("°c");break;
                        case 11: multipleData.setDataType("10cm土壤湿度");multipleData.setUnit("%");break;
                        case 12: multipleData.setDataType("20cm土壤温度");multipleData.setUnit("°c");break;
                        case 13: multipleData.setDataType("20cm土壤湿度");multipleData.setUnit("%");break;
                        case 14: multipleData.setDataType("30cm土壤温度");multipleData.setUnit("°c");break;
                        case 15: multipleData.setDataType("30cm土壤湿度");multipleData.setUnit("%");break;
                        case 16: multipleData.setDataType("40cm土壤温度");multipleData.setUnit("°c");break;
                        case 17: multipleData.setDataType("40cm土壤湿度");multipleData.setUnit("%");break;
                        case 18: multipleData.setDataType("50cm土壤温度");multipleData.setUnit("°c");break;
                        case 19: multipleData.setDataType("50cm土壤湿度");multipleData.setUnit("%");break;
                        case 20: multipleData.setDataType("60cm土壤温度");multipleData.setUnit("°c");break;
                        case 21: multipleData.setDataType("60cm土壤湿度");multipleData.setUnit("%");break;
                        case 22: multipleData.setDataType("土壤电导率");multipleData.setUnit("uS/cm");break;
                        case 23: multipleData.setDataType("土壤PH值");multipleData.setUnit("");break;
                        case 24: multipleData.setDataType("蒸发量");multipleData.setUnit("mm/s");break;
                        case 25: multipleData.setDataType("露点");multipleData.setUnit("°c");break;
                        case 26: multipleData.setDataType("光合有效辐射");multipleData.setUnit("umol*m-2*s-1");break;
                        case 27: multipleData.setDataType("总辐射");multipleData.setUnit("umol*m-2*s-1");break;
                        case 28: multipleData.setDataType("噪音");multipleData.setUnit("db");break;
                        case 29: multipleData.setDataType("PM2.5");multipleData.setUnit("Μg/m3");break;
                        case 30: multipleData.setDataType("PM10");multipleData.setUnit("Μg/m3");break;
                        case 31: multipleData.setDataType("液位计");multipleData.setUnit("cm");break;
                        case 32: multipleData.setDataType("氮");multipleData.setUnit("mg/L");break;
                        case 33: multipleData.setDataType("磷");multipleData.setUnit("mg/L");break;
                        case 34: multipleData.setDataType("钾");multipleData.setUnit("mg/L");break;
                        case 35: multipleData.setDataType("土壤张力15cm");multipleData.setUnit("kPa");break;
                        case 36: multipleData.setDataType("土壤张力30cm");multipleData.setUnit("kPa");break;
                        case 37: multipleData.setDataType("土壤张力45cm");multipleData.setUnit("kPa");break;
                        case 38: multipleData.setDataType("氧气");multipleData.setUnit("%");break;
                        case 39: multipleData.setDataType("乙烯");multipleData.setUnit("ppm");break;
                        case 40: multipleData.setDataType("PT1000");multipleData.setUnit("°C");break;
                        default: multipleSensors.setSensorModel("");
                    }
                    index += 2;

                    // 传感器状态(1字节): 直接十六进制转十进制，表示传感器工作状态(0-7)
                    Integer sensorStatus = Integer.parseInt(hexString.substring(index, index + 2),16);

                    /* ========== 传感器状态映射解析 ========== */
                    // 根据状态编码设置传感器状态描述，支持8种不同的状态类型
                    switch (sensorStatus){
                        case 0: multipleData.setSensorStatus("通讯状态");break;
                        case 1: multipleData.setSensorStatus("故障状态");break;
                        case 2: multipleData.setSensorStatus("预警状态");break;
                        case 3: multipleData.setSensorStatus("保留");break;
                        case 4: multipleData.setSensorStatus("保留");break;
                        case 5: multipleData.setSensorStatus("数据故障状态");break;
                        case 6: multipleData.setSensorStatus("状态故障状态");break;
                        case 7: multipleData.setSensorStatus("通讯故障状态");break;
                        default: multipleSensors.setSensorModel("");
                    }
                    index += 2;

                    /* ========== 数据值解析和处理 ========== */
                    // 数据值(4字节): 小端序转大端序后转十进制，表示传感器的实际测量值
                    Integer data = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 8)),16);

                    // 根据数据类型进行不同的数值处理：特定类型直接使用原值，其他类型除以100并保留两位小数
                    if (!multipleData.getDataType().equals("光照度") &&
                        !multipleData.getDataType().equals("C02浓度") &&
                        !multipleData.getDataType().equals("紫外线强度") &&
                        !multipleData.getDataType().equals("土壤电导率") &&
                        !multipleData.getDataType().equals("光合有效辐射") &&
                        !multipleData.getDataType().equals("总辐射") &&
                        !multipleData.getDataType().equals("噪音") &&
                        !multipleData.getDataType().equals("PM2.5") &&
                        !multipleData.getDataType().equals("PM10")
                    ){
                        // 一般数据类型：除以100并格式化为两位小数
                        double result = (double) data / 100;
                        DecimalFormat decimalFormat = new DecimalFormat("#0.00");
                        String formattedResult = decimalFormat.format(result);
                        multipleData.setData(formattedResult);
                    }else {
                        // 特殊数据类型：直接使用原始数值
                        multipleData.setData(data.toString());
                    }
                    index += 8;

                    // 将解析完成的数据项添加到数据列表
                    dataList.add(multipleData);
                    multipleSensors.setMultipleData(dataList);
                }
                // 将解析完成的传感器添加到传感器列表
                sensorsList.add(multipleSensors);

            }
            // 将所有传感器数据设置到主对象中
            readGreenhouseSensorsDTO.setMultipleSensors(sensorsList);
        }

        /* ========== 数据包尾部字段解析 ========== */
        // SER序列号(1字节): 直接提取十六进制字符串，表示序列号或数据包标识符
        readGreenhouseSensorsDTO.setSer(hexString.substring(index, index + 2));
        index = index+2;

        // 校验位(1字节): 直接提取十六进制字符串，用于数据完整性校验
        readGreenhouseSensorsDTO.setChecksum(hexString.substring(index, index + 2));
        index = index+2;

        // 结束符(1字节): 直接提取十六进制字符串，表示数据包结束标识
        readGreenhouseSensorsDTO.setEnd(hexString.substring(index, index + 2));

        // 返回解析完成的温室传感器数据对象
        return readGreenhouseSensorsDTO;

    }

    /**
     *
     * @param hexString
     * @return
     */
    public static ReadAnalogMonitorDTO readAnalogMonitorParse(String hexString) {
        //打印日志
        log.info("读取模拟量监测传来的数据: {}", hexString);
        //
        ReadAnalogMonitorDTO readAnalogMonitorDTO = new ReadAnalogMonitorDTO();
        //
        // 包头 (7 bytes)
        readAnalogMonitorDTO.setHeader(hexString.substring(0,14));
        //设置下标
        int index = 14;
        //控制码 (1 byte)
        readAnalogMonitorDTO.setControlCode(hexString.substring(index,index+2));
        //下标+2
        index = index+2;
        //数据长度 (1 byte)（数据小端值转成大端值）然后16进制转10进制
        Integer ControlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        readAnalogMonitorDTO.setDataLength(ControlCode.toString());
        //
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (ControlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //数据标识 (2 bytes)
        readAnalogMonitorDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index=index+4;

        //秒分时日月年 HEX (6 bytes)
        String hexDateTime = hexString.substring(index, index + 12);
        readAnalogMonitorDTO.setDateTimeHex(hexDateTime);
        readAnalogMonitorDTO.setDateTime(parseDateTime(hexDateTime));
        //
        index = index+12;

        //网络状态 (1 byte)
        readAnalogMonitorDTO.setNetworkStatus(Integer.parseInt(hexString.substring(index,index+2),16));
        //
        index = index+2;
        //信号强度
        readAnalogMonitorDTO.setSignalStrength("-"+Integer.parseInt(hexString.substring(index,index+2),16));
        //
        index = index+2;

        //网络在线时长 (4 bytes)
        readAnalogMonitorDTO.setOnlineDuration(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+8)),16));
        //
        index = index+8;
        //设备运行状态 (2 bytes)
        readAnalogMonitorDTO.setDeviceStatus(Integer.parseInt(hexString.substring(index,index+4),16));
        //
        index = index+4;
        //主动上传时间间隔 单位秒 uploadInterval
        readAnalogMonitorDTO.setUploadInterval(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16));
        //
        index = index+4;
        //电源电压 (2 bytes, 单位 mV) powerVoltage
        double powerVoltage =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readAnalogMonitorDTO.setPowerVoltage(powerVoltage);
        //
        index = index+4;
        String softwareVersion = hexString.substring(index,index+8);
        Integer softwareVersion1 = Integer.parseInt(softwareVersion.substring(0, 2));
        Integer softwareVersion2 = Integer.parseInt(softwareVersion.substring(2, 4));
        Integer softwareVersion3 = Integer.parseInt(softwareVersion.substring(4, 6));
        Integer softwareVersion4 = Integer.parseInt(softwareVersion.substring(6, 8));
        //软件版本 (4 bytes) softwareVersion
        readAnalogMonitorDTO.setSoftwareVersion(softwareVersion1+"-"+softwareVersion2+"-"+softwareVersion3+"-"+softwareVersion4);
        //
        index = index+8;
        //硬件版本信息
        String version = hexString.substring(index,index+8);
        Integer version1 = Integer.parseInt(version.substring(0, 2), 16);
        Integer version2 = Integer.parseInt(version.substring(2, 4), 16);
        Integer version3 = Integer.parseInt(version.substring(4, 6), 16);
        Integer version4 = Integer.parseInt(version.substring(6, 8), 16);
        readAnalogMonitorDTO.setHardwareVersion(version1+"-"+version2+"-"+version3+"-"+version4);
        //
        index = index+8;
        //文档写的时保留
        readAnalogMonitorDTO.setReserved(hexString.substring(index,index+16));
        //
        index = index+16;
        //无线参数
        readAnalogMonitorDTO.setWirelessParameter(hexString.substring(index,index+20));
        //
        index = index+20;
        //第一路传感器类型 (1 bytes) （0：无，1：压力1，2：压力2,3：液位）
        Integer firstSensorType =  Integer.parseInt(hexString.substring(index,index+2),16);
        readAnalogMonitorDTO.setFirstSensorType(firstSensorType);
        //
        index = index+2;
        // 第一路数据 (2 bytes)
        Integer firstData = Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        //除以1000
        Double firstData2 = firstData / 1000.0;
        if (firstSensorType == 1 || firstSensorType == 2) readAnalogMonitorDTO.setFirstData(firstData2.toString()+"Mpa");
        if (firstSensorType == 3) readAnalogMonitorDTO.setFirstData(firstData2.toString()+"M");
        if (firstSensorType == 0) readAnalogMonitorDTO.setFirstData(firstData2.toString());
        //
        index = index+4;
        // 第一路电压 (2 bytes)
        Integer firstVoltage = Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readAnalogMonitorDTO.setFirstVoltage(firstVoltage);
        //
        index = index+4;
        // 第一路电流 (2 bytes)
        Integer firstCurrent = Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        //除以100
        Double firstCurrent2 =  firstCurrent /100.0;
        readAnalogMonitorDTO.setFirstCurrent(firstCurrent2);
        //
        index = index+4;

        // 第二路传感器类型 (1 bytes) （0：无，1：压力1，2：压力2,3：液位）
        Integer secondSensorType = Integer.parseInt(hexString.substring(index,index+2),16);
        readAnalogMonitorDTO.setSecondSensorType(secondSensorType);
        //
        index = index+2;
        // 第二路数据 (2 bytes)
        Integer secondData = Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        //除以1000
        Double secondData2 = secondData /1000.0;
        //类型为 压力 用Mpa单位
        if (secondSensorType == 1 || secondSensorType == 2) readAnalogMonitorDTO.setSecondData(secondData2.toString()+"Mpa");
        //类型为 液位  用M单位
        if (secondSensorType == 3 ) readAnalogMonitorDTO.setSecondData(secondData2.toString()+"M");
        //类型为无 没有单位
        if (secondSensorType == 0) readAnalogMonitorDTO.setSecondData(secondData2.toString());
        //
        index = index+4;
        //第二路电压 (2 bytes)
        Integer secondVoltage = Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        readAnalogMonitorDTO.setSecondVoltage(secondVoltage);
        //
        index = index+4;
        //第二路电流 (2 bytes)
        Integer secondCurrent = Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        //除以100
        Double secondCurrent2 = secondCurrent /100.0;
        readAnalogMonitorDTO.setSecondCurrent(secondCurrent2);
        //
        index = index+4;
        // 开关检测1状态 (1 bytes)
        Integer switchDetection1Status = Integer.parseInt(hexString.substring(index,index+2),16);
        readAnalogMonitorDTO.setSwitchDetection1Status(switchDetection1Status.toString());
        //
        index = index+2;
        // 开关检测2状态 (1 bytes)
        Integer switchDetection2Status = Integer.parseInt(hexString.substring(index,index+2),16);
        readAnalogMonitorDTO.setSwitchDetection2Status(switchDetection2Status.toString());
        //
        index = index+2;
        // 第一路输出状态 (1 bytes)
        Integer firstOutputStatus = Integer.parseInt(hexString.substring(index,index+2),16);
        readAnalogMonitorDTO.setFirstOutputStatus(firstOutputStatus.toString());
        //
        index = index+2;
        // 第二路输出状态 (1 bytes)
        Integer secondOutputStatus = Integer.parseInt(hexString.substring(index,index+2),16);
        readAnalogMonitorDTO.setSecondOutputStatus(secondOutputStatus.toString());
        //
        index = index+2;
        //模拟量输出百分比 (1 bytes)
        Integer analogMonitorOutputPercentage = Integer.parseInt(hexString.substring(index,index+2),16);
        readAnalogMonitorDTO.setAnalogMonitorOutputPercentage(analogMonitorOutputPercentage.toString());
        //
        index = index+2;
        // SER，序列号或标识符
        readAnalogMonitorDTO.setSer(hexString.substring(index, index + 2));
        //
        index = index+2;
        //校验位 (1 byte)
        readAnalogMonitorDTO.setChecksum(hexString.substring(index, index + 2));
        //
        index = index+2;
        //结束符 (1 byte)
        readAnalogMonitorDTO.setEnd(hexString.substring(index, index + 2));

        return readAnalogMonitorDTO;
    }

    public static RedPassageParameterDTO readPassageParameter(String hexString) {

        //打印日志
        log.info("读取模拟量通道传来的数据: {}", hexString);
        //
        RedPassageParameterDTO RedPassageParameterDTO = new RedPassageParameterDTO();
        // 包头 (7 bytes)
        RedPassageParameterDTO.setHeader(hexString.substring(0,14));
        //设置下标
        int index = 14;
        //控制码 (1 byte)
        RedPassageParameterDTO.setControlCode(hexString.substring(index,index+2));
        //下标+2
        index = index+2;
        //数据长度 (1 byte)（数据小端值转成大端值）然后16进制转10进制
        Integer ControlCode = Integer.parseInt(CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        RedPassageParameterDTO.setDataLength(ControlCode.toString());
        //
        int actualLength = hexString.length() - 24; // 去掉前面的5个字节（包头+控制码+数据长度+数据校验+结束符）
        //数据长度*2是 字符串长度
        if (ControlCode*2 != actualLength){
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配！");
        }
        //
        index = index+4;
        //数据标识 (2 bytes)
        RedPassageParameterDTO.setDataIdentifier(CommonUtil.reverse(hexString.substring(index,index+4)));
        //
        index=index+4;
        //通道号（00：通道1，01：通道2，02：通道3，03：通道4）1字节
        RedPassageParameterDTO.setPassage(Integer.parseInt(hexString.substring(index,index+2),16));
        //
        index = index+2;
        //类型（0：无，1：0-5V，2：0-10V,3：4-20mA，4：0.5-4.5V）1字节
        RedPassageParameterDTO.setType(Integer.parseInt(hexString.substring(index,index+2),16));
        //
        index = index+2;
        //传感器类型（0：无，1：压力1，2：压力2,3：液位）1字节
        RedPassageParameterDTO.setSensorType(Integer.parseInt(hexString.substring(index,index+2),16));
        //
        index = index+2;
        //    //最小值 2字节
        RedPassageParameterDTO.setMinValue(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16));
        //
        index = index+4;
        //最大值 2字节
        RedPassageParameterDTO.setMaxValue(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16));
        //
        index = index+4;
        //保护阈值倍数 2字节
        Integer thresholdMultiple =  Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16);
        double thresholdMultipleDouble = thresholdMultiple / 100.0;
        DecimalFormat df = new DecimalFormat("0.00");
        String thresholdMultipleStr = df.format(thresholdMultipleDouble);
        RedPassageParameterDTO.setThresholdMultiple(thresholdMultipleStr);
        //
        index = index+4;
        //正常值 2字节
        RedPassageParameterDTO.setNormalValue(Integer.parseInt(CommonUtil.reverse(hexString.substring(index,index+4)),16));
        //
        index = index+4;
        //动作模式（0：高于，1：低于，2：高于低于，3：保持）1字节
        RedPassageParameterDTO.setActionMode(Integer.parseInt(hexString.substring(index,index+2),16));
        //
        index = index+2;
        // SER，序列号或标识符
        RedPassageParameterDTO.setSer(hexString.substring(index, index + 2));
        //
        index = index+2;
        //校验位 (1 byte)
        RedPassageParameterDTO.setChecksum(hexString.substring(index, index + 2));
        //
        index = index+2;
        //结束符 (1 byte)
        RedPassageParameterDTO.setEnd(hexString.substring(index, index + 2));

        return RedPassageParameterDTO;

    }

    // 将HEX格式的日期时间转换为LocalDateTime
    private static LocalDateTime parseDateTime(String hexDateTime) {
        /*String time = CommonUtil.reverse(hexDateTime);
        LocalDate localDate = LocalDate.now();
        int year = Integer.parseInt(time.substring(0, 2), 16);
        int month = Integer.parseInt(time.substring(2, 4), 16);
        int day = Integer.parseInt(time.substring(4, 6), 16);
        int hour = Integer.parseInt(time.substring(6, 8), 16);
        int minute = Integer.parseInt(time.substring(8, 10), 16);
        int second = Integer.parseInt(time.substring(10, 12), 16);
        if (year <= 0 || month <= 0 || day <= 0) {
            throw new RuntimeException("时间格式错误");
        }
        String s = String.valueOf(localDate.getYear()).substring(0, 2) + (year < 10 ? ("0" + year) : year) + "-" +
                (month < 10 ? ("0" + month) : month) + "-" +
                (day < 10 ? ("0" + day) : day)
                + " " + (hour < 10 ? ("0" + hour) : hour) + ":" +
                (minute < 10 ? ("0" + minute) : minute) + ":" +
                (second < 10 ? ("0" + second) : second);
        System.out.println(s);*/


        // return LocalDateTime.now();

        int second = Integer.parseInt(hexDateTime.substring(0, 2), 16);
        int minute = Integer.parseInt(hexDateTime.substring(2, 4), 16);
        int hour = Integer.parseInt(hexDateTime.substring(4, 6), 16);
        int day = Integer.parseInt(hexDateTime.substring(6, 8), 16);
        int month = Integer.parseInt(hexDateTime.substring(8, 10), 16);
        int year = Integer.parseInt(hexDateTime.substring(10, 12), 16) + 2000; // Assuming the year is 2000+year

        return LocalDateTime.of(year, month, day, hour, minute, second);

    }

    private static void validateChecksum(String data, String checksum) {
        // 此处应包含具体的校验逻辑
        // 示例：简单校验位运算
        int computedChecksum = 0;
        for (int i = 0; i < data.length(); i += 2) {
            computedChecksum ^= Integer.parseInt(data.substring(i, i + 2), 16);
        }
        if (computedChecksum != Integer.parseInt(checksum.substring(0, 2), 16)) {
            throw new IllegalArgumentException("校验失败！");
        }
    }




}
