package com.memory;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Component
public class Redis {
    @Value("${redis.url}")
    String url;

    @Value("${redis.db}")
    Integer db;

    /*@Value("${redis.password}")
    String password;*/

    @Value("${redis.port}")
    Integer port;

    public static volatile JedisPool jedisPool = null;

    @PostConstruct
    void RedisIni() {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxIdle(100);
        jedisPoolConfig.setMinIdle(10);
        jedisPoolConfig.setMaxWait(Duration.ofSeconds(2000));
        jedisPoolConfig.setMaxTotal(10);
        jedisPoolConfig.setJmxEnabled(false);
        jedisPool = new JedisPool(jedisPoolConfig, url, port, 2000, null, db);
    }
}
