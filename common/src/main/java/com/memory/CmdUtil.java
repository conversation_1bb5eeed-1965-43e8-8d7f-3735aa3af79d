package com.memory;

import com.taosdata.jdbc.utils.StringUtils;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.util.CharsetUtil;

import java.math.BigDecimal;

public class CmdUtil {
    public enum functionCode {
        F9002C02
    }


    /**
     * 读传感器数据
     *
     * @param no
     * @return
     */
    public static String CMD019002(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("01");//
        String data = "02900101" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        // 68 0100002280 68 01 05 01 02 90 01 00 01 0C 16
        return sb.toString().toUpperCase();
    }

    public static String CMD01900C(String no, String ser, Integer num) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("01");//
        String data = "0C90" + CommonUtil.reverse("0000" + Integer.toHexString(num)).substring(0, 4) + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD019011(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("01");//
        String data = "1190" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD019012(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("01");//
        String data = "1290" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD02A001(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("02");//
        String data = "01A0" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD02A002(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("02");//
        String data = "02A0" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD03800A(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");//
        String data = "0A80" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD03800B(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");//
        String data = "0B80" + "FF0000000000" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD04800A(String no, String ser, Integer billMethod, BigDecimal electricPrice, BigDecimal waterPrice, BigDecimal timePrice) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");//
        String _billMethod = CommonUtil.reverse("00" + Integer.toHexString(billMethod)).substring(0, 2);
        String _electricPrice = CommonUtil.reverse("0000" + Integer.toHexString(electricPrice.multiply(BigDecimal.valueOf(100)).intValue())).substring(0, 4);
        String _waterPrice = CommonUtil.reverse("0000" + Integer.toHexString(waterPrice.multiply(BigDecimal.valueOf(100)).intValue())).substring(0, 4);
        String _timePrice = CommonUtil.reverse("0000" + Integer.toHexString(timePrice.multiply(BigDecimal.valueOf(100)).intValue())).substring(0, 4);
        String data = "0A80" + _billMethod + _electricPrice + _waterPrice + _timePrice + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048001(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append("AAAAAAAAAA");
        sb.append("68");
        sb.append("04");//
        String data = "0180" + CommonUtil.reverse(no) + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048002(String no, String ser, Integer year, Integer month, Integer day, Integer hour, Integer minute, Integer second) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");//
        String data = "0280" + Integer.toHexString(second) + Integer.toHexString(minute) + Integer.toHexString(hour) + Integer.toHexString(day) + Integer.toHexString(month) + Integer.toHexString(year) + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038002(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");//
        String data = "0280" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048003(String no, String ser, Integer order, boolean state, String ip, String port) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String strIP = (CommonUtil.convertStringToHex(ip) + "000000000000000000000000000000").substring(0, 30);
        String strPort = (CommonUtil.convertStringToHex(port) + "0000000000").substring(0, 10);
        String data = "0380" + CommonUtil.reverse(Integer.toHexString(order)) + CommonUtil.reverse(Integer.toHexString(state ? 1 : 0)) + strIP + strPort + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038003(String no, String ser, Integer order) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "0380" + CommonUtil.reverse(Integer.toHexString(order)) + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048004(String no, String ser, Integer model, Integer loginType) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String data = "0480" + CommonUtil.reverse(Integer.toHexString(model)) + CommonUtil.reverse(Integer.toHexString(loginType)) + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038004(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "0480" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038006(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "0680" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048005(String no, String ser, String APN) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String strAPN = (CommonUtil.convertStringToHex(APN) + "000000000000000000000000000000000000000000000000000000000000").substring(0, 60);
        String data = "0580" + strAPN + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048007(String no, String ser, Integer uploadSwitch, Integer timeout, Integer heart, Integer logSwitch, Integer uploadPattern, Integer uploadInterval) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String data = "0780" + CommonUtil.reverse(Integer.toHexString(uploadSwitch)) +
                CommonUtil.reverse(Integer.toHexString(timeout)) +
                CommonUtil.reverse(Integer.toHexString(heart)) +
                CommonUtil.reverse(Integer.toHexString(logSwitch)) +
                CommonUtil.reverse(Integer.toHexString(uploadPattern)) +
                CommonUtil.reverse(Integer.toHexString(uploadInterval) + "00") + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038007(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "0780" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038009(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "0980" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038005(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String data = "0580" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038010(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "1080" + "FF" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038016(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "1680" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038014(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "1480" + "FF" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD019014(String no, String ser, String str) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("01");
        String data = "1490" + str + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }
    public static String CMD048016(String no, String ser, String str) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String data = "1680" + str + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }



    public static String CMD019015(String no, String ser, String str) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("01");
        String data = "1590" + str + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD02A003(String no, String ser, String str) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("02");
        String data = "03A0" + str + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD02A004(String no, String ser, String str) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("02");
        String data = "04A0" + str + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD02A005(String no, String ser, String str) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("02");
        String data = "05A0" + str + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048006(String no, String ser, String DNS) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String strDNS = (CommonUtil.convertStringToHex(DNS) + "000000000000000000000000000000000000000000000000000000000000").substring(0, 60);
        String data = "0680" + strDNS + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048008(String no, String ser, String productId, String scriptId) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String strProductId = (CommonUtil.convertStringToHex(productId) + "00000000000000000000").substring(0, 20);
        String strScriptId = (CommonUtil.convertStringToHex(scriptId) + "0000000000").substring(0, 10);

        String data = "0880" + strProductId + strScriptId + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048009(String no, String ser, Integer baud) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String data = "0980" + CommonUtil.reverse(Integer.toHexString(baud)) + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD048010(String no, String ser, String texts) {
        StringBuilder sb = new StringBuilder();
        // 功能(1,1:新增,2:修改,3:删除)，通道号(1),传感器型号(2),ID(5)，...，SER
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("04");
        String data = "1080";
        data += texts;
        data += ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }


    public static String CMD038008(String no, String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append(CommonUtil.reverse(no));
        sb.append("68");
        sb.append("03");
        String data = "0880" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }

    public static String CMD038001(String ser) {
        StringBuilder sb = new StringBuilder();
        sb.append("68");
        sb.append("AAAAAAAAAA");
        sb.append("68");
        sb.append("03");//
        String data = "0180" + ser;
        sb.append(CommonUtil.getLength(data));//长度
        sb.append(data);
        sb.append(CommonUtil.sum(sb.toString()));
        sb.append("16");
        return sb.toString().toUpperCase();
    }
}
