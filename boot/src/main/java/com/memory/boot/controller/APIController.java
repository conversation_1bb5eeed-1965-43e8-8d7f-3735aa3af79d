package com.memory.boot.controller;

import com.memory.ClientInfo;
import com.memory.Redis;
import com.memory.ServerUtil;
import com.memory.TcpSnoopServerHandler;
import com.memory.boot.entity.CmdVo;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.util.CharsetUtil;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.List;

import static com.memory.ServerUtil.sendCmd;
import static com.memory.TcpSnoopServerHandler.clients;
import static com.memory.TcpSnoopServerHandler.closeChannel;


@RestController
public class APIController {



    //即时命令
    @PostMapping(value = "/cmd/1")
    public String add1(@RequestBody CmdVo cv) {
        System.out.println("/cmd/1 " + cv.toString());
        if (null == cv || !StringUtils.hasText(cv.getCmd()) || !StringUtils.hasText(cv.getDeviceNo())) {
            return "ERROR";
        }
        return sendCmd(cv.getDeviceNo(), cv.getCmd()).get("message").toString();
    }


    //缓存命令
    @PostMapping(value = "/cmd/2")
    public String add2(@RequestBody CmdVo cv) {
        System.out.println("/cmd/2 " + cv.toString());
        if (null == cv || !StringUtils.hasText(cv.getCmd()) || !StringUtils.hasText(cv.getDeviceNo())) {
            return "ERROR";
        }
        try (Jedis redis = Redis.jedisPool.getResource()) {
            redis.rpush(cv.getDeviceNo(), cv.getCmd());
        }
        return "SUCCESS";
    }

    @PostMapping(value = "/cmd/3")
    public String add3(@RequestBody CmdVo cv) {
        System.out.println("/cmd/3 " + cv.toString());
        String status = add1(cv);
        if (status.equals("OFFLINE"))
            status = add2(cv);
        return status;
    }

    @PostMapping(value = "/cmd/3/uuid")
    public HashMap<String, Object> add3Uuid(@RequestBody CmdVo cv) {
        System.out.println("/cmd/3/uuid " + cv.toString());
        return sendCmd(cv.getDeviceNo(), cv.getCmd());
    }


    // 缓存命令数量
    @GetMapping(value = "/cmd/2/list")
    public List<String> cmd2(String deviceNo) {
        if (!StringUtils.hasText(deviceNo)) {
            return null;
        }
        try (Jedis redis = Redis.jedisPool.getResource()) {
            return redis.lrange(deviceNo, 0, -1);
        }
    }

    // 队列积压查询
    @GetMapping(value = "/queue/list")
    public List<String> queue() {
        try (Jedis redis = Redis.jedisPool.getResource()) {
            return redis.lrange("share-push-queue", 0, -1);
        }
    }

    @GetMapping(value = "/mirror")
    public ClientInfo mirror(String deviceNo) {
        ClientInfo ci = clients.get(deviceNo);
        if (null != ci && null != ci.getChannel()) {
            ci.setRemote(ci.getChannel().remoteAddress());
            ci.setLocal(ci.getChannel().localAddress());
            ci.setPipeline(ci.getChannel().pipeline());
        }
        return clients.get(deviceNo);
    }

    @GetMapping(value = "/mirror/close")
    public String close(String deviceNo) {
        ClientInfo ci = clients.get(deviceNo);
        if (null == ci) return "ERROR";
        closeChannel(ci.getChannel(), null, "mirrorclose");
        return "SUCCESS";
    }

    @GetMapping(value = "/mirrors")
    public String mirrors() {
        return clients.toString();
    }
}
