/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package com.memory.boot;

import cn.hutool.core.date.DateTime;
import com.memory.*;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.ssl.SslContext;
import io.netty.util.AttributeKey;
import io.netty.util.CharsetUtil;
import jakarta.annotation.PostConstruct;

import org.joda.time.Period;
import org.joda.time.Seconds;
import org.springframework.beans.factory.annotation.Value;
import redis.clients.jedis.Jedis;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import java.util.concurrent.TimeUnit;


import static com.memory.ServerUtil.sendCmd;
import static com.memory.ServerUtil.transfer;
import static com.memory.TcpSnoopServerHandler.clients;
import static com.memory.TcpSnoopServerHandler.closeChannel;

/**
 * An HTTP server that sends back the content of the received HTTP request
 * in a pretty plaintext form.
 */

public class TcpSnoopServer {

    public static void start(int PORT, String pushUrl) throws Exception {
        System.out.println("netty 启动 " + PORT);
        // Configure SSL.
        final SslContext sslCtx = ServerUtil.buildSslContext();
        // Configure the server.
        EventLoopGroup bossGroup = new NioEventLoopGroup(1);
        EventLoopGroup workerGroup = new NioEventLoopGroup();
        try {
            ServerBootstrap b = new ServerBootstrap();
            b.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .handler(new LoggingHandler(LogLevel.INFO))
                    .childHandler(new TcpSnoopServerInitializer(sslCtx, pushUrl));
            // 半小时剔除一次 3小时没上线过的设备
            workerGroup.next().scheduleAtFixedRate(() -> {
                System.out.println("客户端信息超时检测...");
                long unixtime = System.currentTimeMillis() / 1000;
                clients.keySet().forEach(client -> {
                    try {
                        if (Math.abs(unixtime - clients.get(client).getUnixtime()) > 3 * 60 * 60) {
                            System.out.println(client + "信息超时 移除");
                            closeChannel(clients.get(client).getChannel(), null, "scheduleAtFixedRate");
                            clients.remove(client);
                        }
                    } catch (Exception ex) {
                        System.out.println(client + " 信息超时异常 " + ex.getMessage());
                    }
                });
            }, 60, 30 * 60, TimeUnit.SECONDS);
            Channel ch = b.bind(PORT).sync().channel();
            ch.closeFuture().sync();
        } finally {
            bossGroup.shutdownGracefully();
            workerGroup.shutdownGracefully();
        }
    }
}
