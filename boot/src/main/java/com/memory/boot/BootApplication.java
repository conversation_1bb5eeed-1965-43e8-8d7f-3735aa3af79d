package com.memory.boot;

import com.memory.Redis;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

@SpringBootApplication
@ComponentScan("com.memory")
public class BootApplication implements CommandLineRunner {
    @Value("${netty.port}")
    private int nettyport;

    @Value("${netty.push.url}")
    private String pushUrl;



    public static void main(String[] args) {
        SpringApplication.run(BootApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println(nettyport);
        System.out.println("Processors " + Runtime.getRuntime().availableProcessors());
        TcpSnoopServer.start(nettyport, pushUrl);
    }
}
