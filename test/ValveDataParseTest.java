package test;

import com.memory.DataParser;
import com.memory.dto.ReadValveDateDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 阀控数据解析测试
 * 用于验证您的十六进制数据解析
 */
public class ValveDataParseTest {
    
    private static final Logger log = LoggerFactory.getLogger(ValveDataParseTest.class);
    
    public static void main(String[] args) {
        // 您的实际数据
        String hexData = "68010000258668815C0086902D200F0207193F333F00000000040807C90F2506300000650000000000003E0000025A32320000000000000000000000000000000000000000000000000000AA000064640008000000070000000C0000009209000000000000017016";
        
        log.info("开始测试阀控数据解析");
        log.info("原始数据: {}", hexData);
        log.info("数据长度: {} 字符 ({} 字节)", hexData.length(), hexData.length()/2);
        
        try {
            // 分析数据结构
            analyzeDataStructure(hexData);
            
            // 尝试解析数据
            ReadValveDateDto result = DataParser.readValveDateParse(hexData);
            
            if (result != null) {
                log.info("解析成功！");
                printParseResult(result);
            } else {
                log.error("解析失败：返回null");
            }
            
        } catch (Exception e) {
            log.error("解析异常: {}", e.getMessage(), e);
            
            // 提供解决建议
            provideSolution(hexData, e);
        }
    }
    
    /**
     * 分析数据结构
     */
    private static void analyzeDataStructure(String hexData) {
        log.info("=== 数据结构分析 ===");
        
        if (hexData.length() >= 14) {
            String header = hexData.substring(0, 14);
            log.info("包头: {}", header);
        }
        
        if (hexData.length() >= 16) {
            String controlCode = hexData.substring(14, 16);
            log.info("控制码: {}", controlCode);
        }
        
        if (hexData.length() >= 20) {
            String dataLength = hexData.substring(16, 20);
            log.info("数据长度(小端序): {}", dataLength);
            
            // 转换为大端序
            String reversedLength = dataLength.substring(2, 4) + dataLength.substring(0, 2);
            int lengthValue = Integer.parseInt(reversedLength, 16);
            log.info("数据长度(大端序): {} = {} 字节", reversedLength, lengthValue);
        }
        
        if (hexData.length() >= 24) {
            String protocolId = hexData.substring(20, 24);
            log.info("协议标识(小端序): {}", protocolId);
            
            // 转换为大端序
            String reversedProtocol = protocolId.substring(2, 4) + protocolId.substring(0, 2);
            log.info("协议标识(大端序): {}", reversedProtocol);
        }
    }
    
    /**
     * 打印解析结果
     */
    private static void printParseResult(ReadValveDateDto result) {
        log.info("=== 解析结果 ===");
        log.info("包头: {}", result.getHeader());
        log.info("控制码: {}", result.getControlCode());
        log.info("数据长度: {}", result.getDataLength());
        log.info("协议标识: {}", result.getDataIdentifier());
        log.info("数据质量: {}", result.getDataQuality());
        
        if (result.getDateTime() != null) {
            log.info("时间: {}", result.getDateTime());
        }
        
        if (result.getNetworkStatus() != 0) {
            log.info("网络状态: {}", result.getNetworkStatus());
        }
        
        if (result.getSignalStrength() != 0) {
            log.info("信号强度: {}", result.getSignalStrength());
        }
        
        if (result.getPowerVoltage() != 0) {
            log.info("电源电压: {} mV", result.getPowerVoltage());
        }
        
        if (result.getBatteryLevel() != null) {
            log.info("电池电量: {}%", result.getBatteryLevel());
        }
    }
    
    /**
     * 提供解决方案
     */
    private static void provideSolution(String hexData, Exception e) {
        log.info("=== 解决方案建议 ===");
        
        if (e.getMessage().contains("数据长度不正确")) {
            log.info("问题：数据长度不匹配");
            log.info("当前数据长度: {} 字节", hexData.length()/2);
            log.info("期望数据长度: 104 字节");
            log.info("解决方案：");
            log.info("1. 检查数据是否完整传输");
            log.info("2. 确认设备是否发送了完整的数据包");
            log.info("3. 检查网络传输是否有丢包");
        }
        
        if (e.getMessage().contains("数据长度与报文中指定的长度不匹配")) {
            log.info("问题：报文长度字段与实际数据不匹配");
            log.info("解决方案：");
            log.info("1. 检查数据长度字段的解析是否正确");
            log.info("2. 确认小端序转大端序的转换");
            log.info("3. 验证数据包的完整性");
        }
    }
}
