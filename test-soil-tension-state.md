# 土壤张力参数状态跟踪测试文档

## 修复内容

已成功为以下参数添加状态跟踪：

### 土壤张力参数
- `soilTension15cm` - 15cm土壤张力
- `soilTension30cm` - 30cm土壤张力  
- `soilTension45cm` - 45cm土壤张力

### NPK参数（额外添加）
- `N` - 氮含量
- `P` - 磷含量
- `K` - 钾含量

## 实现细节

### 状态跟踪逻辑
```java
// 添加NPK和土壤张力参数的状态跟踪
useMap.put("N", 1);
useMap.put("P", 1);
useMap.put("K", 1);
useMap.put("soilTension15cm", 1);
useMap.put("soilTension30cm", 1);
useMap.put("soilTension45cm", 1);
hm.put("use-N", 1);
hm.put("use-P", 1);
hm.put("use-K", 1);
hm.put("use-soilTension15cm", 1);
hm.put("use-soilTension30cm", 1);
hm.put("use-soilTension45cm", 1);
```

### 状态值含义
- `0` = 无数据可用
- `1` = 数据可用

## 测试验证

### 预期结果
当传感器数据被解析时，返回的HashMap应包含：

1. **数据字段**：
   - `"N": [氮含量值]`
   - `"P": [磷含量值]`
   - `"K": [钾含量值]`
   - `"soilTension15cm": [15cm土壤张力值]`
   - `"soilTension30cm": [30cm土壤张力值]`
   - `"soilTension45cm": [45cm土壤张力值]`

2. **状态字段**：
   - `"use-N": 1`
   - `"use-P": 1`
   - `"use-K": 1`
   - `"use-soilTension15cm": 1`
   - `"use-soilTension30cm": 1`
   - `"use-soilTension45cm": 1`

3. **use-state对象**：
   ```json
   "use-state": {
     "N": 1,
     "P": 1,
     "K": 1,
     "soilTension15cm": 1,
     "soilTension30cm": 1,
     "soilTension45cm": 1,
     // ... 其他传感器状态
   }
   ```

## 一致性保证

修复后的实现确保了：
1. 解析的数据参数与其对应的可用性状态保持一致
2. 遵循现有的状态跟踪模式
3. 与其他传感器参数的处理方式保持统一

## 文件修改

- **文件**: `common/src/main/java/com/memory/CommonUtil.java`
- **方法**: `parseSensorData(String str)`
- **行数**: 995-1007
