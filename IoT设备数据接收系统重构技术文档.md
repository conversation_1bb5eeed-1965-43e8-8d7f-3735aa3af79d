# IoT设备数据接收系统重构技术文档

## 目录
1. [重构概述](#1-重构概述)
2. [技术栈升级方案](#2-技术栈升级方案)
3. [系统架构重构设计](#3-系统架构重构设计)
4. [数据解析框架重构](#4-数据解析框架重构)
5. [代码结构优化](#5-代码结构优化)
6. [数据存储架构优化](#6-数据存储架构优化)
7. [配置管理方案](#7-配置管理方案)
8. [实施计划](#8-实施计划)
9. [测试策略](#9-测试策略)
10. [部署方案](#10-部署方案)
11. [风险评估与应对](#11-风险评估与应对)

## 1. 重构概述

### 1.1 重构目标
基于现有IoT设备数据接收系统的全面分析，本次重构旨在解决以下核心问题：
- **代码耦合度高**：TcpSnoopServerHandler类承担过多职责，600+行代码难以维护
- **扩展性差**：新增设备类型需要修改核心类，违反开闭原则
- **异常处理不统一**：缺乏统一的异常处理机制和错误码体系
- **代码规范性不足**：未严格遵循阿里巴巴Java开发手册规范

### 1.2 重构原则
- **功能对等性**：保持现有所有功能不变
- **API兼容性**：确保HTTP API向后兼容
- **性能提升**：目标性能提升20%
- **可维护性**：提高代码可读性和可维护性

## 2. 技术栈升级方案

### 2.1 版本升级清单

| 组件 | 当前版本 | 目标版本 | 升级理由 |
|------|----------|----------|----------|
| Spring Boot | 3.2.3 | 3.3.4 | 性能优化、安全补丁 |
| Netty | 4.1.107.Final | 4.1.114.Final | 内存泄漏修复、性能优化 |
| Redis Client | Jedis 5.0.0 | Lettuce 6.4.0 | 异步支持、连接池优化 |
| TDengine JDBC | 3.2.8 | 3.3.0 | 连接稳定性提升 |
| FastJSON2 | 2.0.47 | Jackson 2.17.2 | 安全性提升、生态完善 |
| HuTool | 5.8.26 | 5.8.32 | 工具方法增强 |

### 2.2 依赖管理优化

```xml
<!-- 父POM依赖管理 -->
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>3.3.4</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-bom</artifactId>
            <version>4.1.114.Final</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson</groupId>
            <artifactId>jackson-bom</artifactId>
            <version>2.17.2</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>

<!-- JSON处理库替换 -->
<dependencies>
    <!-- 移除FastJSON2 -->
    <!--
    <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2</artifactId>
        <version>2.0.47</version>
    </dependency>
    -->

    <!-- 添加Jackson -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr310</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-parameter-names</artifactId>
    </dependency>
</dependencies>
```

### 2.3 Jackson配置和使用

#### 2.3.1 Jackson配置类

```java
package com.memory.iot.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson配置
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册Java 8时间模块
        mapper.registerModule(new JavaTimeModule());

        // 禁用将日期写为时间戳
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 设置属性命名策略（可选）
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

        // 忽略未知属性
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return mapper;
    }
}
```

#### 2.3.2 JSON工具类

```java
package com.memory.iot.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * JSON工具类 - 替换FastJSON2
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class JsonUtil {

    private static ObjectMapper objectMapper;

    @Autowired
    public void setObjectMapper(ObjectMapper objectMapper) {
        JsonUtil.objectMapper = objectMapper;
    }

    /**
     * 对象转JSON字符串
     *
     * @param object 对象
     * @return JSON字符串
     */
    public static String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转对象
     *
     * @param json JSON字符串
     * @param clazz 目标类型
     * @return 对象
     */
    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON转对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转Map
     *
     * @param json JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> parseToMap(String json) {
        try {
            return objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.error("JSON转Map失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
```

#### 2.3.3 FastJSON2到Jackson迁移指南

**迁移对照表**：

| FastJSON2 | Jackson | 说明 |
|-----------|---------|------|
| `JSONObject.toJSONString(obj)` | `JsonUtil.toJsonString(obj)` | 对象序列化 |
| `JSONObject.parseObject(json, Class)` | `JsonUtil.parseObject(json, Class)` | JSON反序列化 |
| `JSONObject.parseObject(json)` | `JsonUtil.parseToMap(json)` | 转为Map |
| `@JSONField` | `@JsonProperty` | 字段映射注解 |

**安全性优势**：
- **类型安全**：Jackson默认禁用autoType，避免反序列化漏洞
- **社区支持**：Spring Boot官方推荐，生态更完善
- **性能稳定**：在大数据量处理时性能更稳定
- **标准兼容**：更好的JSON标准兼容性

## 3. 系统架构重构设计

### 3.1 新架构图

```mermaid
graph TB
    subgraph "IoT设备层"
        D1[智能控制器]
        D2[智能采集器]
        D3[杀虫灯]
        D4[智能阀控器]
        D5[温室大棚]
        D6[其他设备...]
    end

    subgraph "网络接入层 (iot-network)"
        TCP[TCP Server]
        PD[PackageDecoder]
        CH[ChannelHandler]
        CM[ConnectionManager<br/>连接管理器]
    end

    subgraph "协议解析层 (iot-protocol)"
        PF[ProtocolFactory<br/>协议工厂]
        PA1[智能控制器解析器]
        PA2[杀虫灯解析器]
        PA3[阀控器解析器]
        PA4[温室解析器]
        PAX[其他解析器...]
    end

    subgraph "业务处理层 (iot-service)"
        DS[DeviceService<br/>设备服务]
        CS[CommandService<br/>命令服务]
        AS[AuthService<br/>认证服务]
        NS[NotificationService<br/>通知服务]
    end

    subgraph "数据访问层 (iot-repository)"
        DR[DeviceRepository]
        CR[CommandRepository]
        SR[SensorDataRepository]
    end

    subgraph "存储层"
        Redis[(Redis<br/>缓存/队列)]
        TD[(TDengine<br/>时序数据库)]
    end

    subgraph "外部集成层 (iot-integration)"
        HTTP[HttpPushService]
        EXT[外部IoT平台]
    end

    D1 --> TCP
    D2 --> TCP
    D3 --> TCP
    D4 --> TCP
    D5 --> TCP
    D6 --> TCP

    TCP --> PD
    PD --> CH
    CH --> CM
    CH --> PF

    PF --> PA1
    PF --> PA2
    PF --> PA3
    PF --> PA4
    PF --> PAX

    PA1 --> DS
    PA2 --> DS
    PA3 --> DS
    PA4 --> DS
    PAX --> DS

    DS --> DR
    CS --> CR
    AS --> DR
    NS --> HTTP

    DR --> Redis
    DR --> TD
    CR --> Redis
    SR --> TD

    HTTP --> EXT
```

### 3.2 模块划分

#### 3.2.1 iot-common（公共模块）
- **基础工具类**：CommonUtil、DateUtil等
- **常量定义**：协议常量、错误码等
- **基础异常**：业务异常基类
- **配置类**：系统配置属性

#### 3.2.2 iot-protocol（协议解析模块）
- **协议接口**：ProtocolParser接口
- **解析器实现**：各设备类型的具体解析器
- **协议工厂**：解析器注册和发现
- **数据模型**：各种DTO类

#### 3.2.3 iot-network（网络通信模块）
- **Netty服务器**：TCP服务器启动和配置
- **连接管理**：设备连接状态管理
- **数据编解码**：协议数据包处理
- **心跳检测**：连接保活机制

#### 3.2.4 iot-service（业务服务模块）
- **设备服务**：设备管理和状态维护
- **命令服务**：命令下发和状态跟踪
- **认证服务**：设备认证和权限管理
- **通知服务**：数据推送和事件通知

#### 3.2.5 iot-repository（数据访问模块）
- **Repository接口**：数据访问抽象
- **Redis操作**：缓存和队列操作
- **TDengine操作**：时序数据存储
- **事务管理**：数据一致性保证

#### 3.2.6 iot-web（Web接口模块）
- **REST API**：HTTP接口实现
- **异常处理**：全局异常处理器
- **参数验证**：请求参数校验
- **API文档**：Swagger文档生成

## 4. 数据解析框架重构

### 4.1 协议解析器接口设计

```java
package com.memory.iot.protocol.core;

import com.memory.iot.protocol.model.ParseResult;
import com.memory.iot.protocol.model.ProtocolType;

/**
 * 协议解析器接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface ProtocolParser<T> {

    /**
     * 获取支持的协议类型
     *
     * @return 协议类型
     */
    ProtocolType getSupportedProtocol();

    /**
     * 检查是否支持该数据包
     *
     * @param payload 原始数据包
     * @return 是否支持
     */
    boolean supports(String payload);

    /**
     * 解析数据包
     *
     * @param payload 原始数据包
     * @return 解析结果
     * @throws ProtocolParseException 解析异常
     */
    ParseResult<T> parse(String payload) throws ProtocolParseException;

    /**
     * 验证解析结果
     *
     * @param result 解析结果
     * @return 验证是否通过
     */
    default boolean validate(ParseResult<T> result) {
        return result != null && result.getData() != null;
    }
}
```

### 4.2 协议工厂实现

```java
package com.memory.iot.protocol.factory;

import com.memory.iot.protocol.core.ProtocolParser;
import com.memory.iot.protocol.model.ProtocolType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 协议解析器工厂
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class ProtocolParserFactory {

    private final Map<ProtocolType, ProtocolParser<?>> parserMap = new ConcurrentHashMap<>();

    @Autowired
    private List<ProtocolParser<?>> parsers;

    @PostConstruct
    public void init() {
        for (ProtocolParser<?> parser : parsers) {
            ProtocolType protocolType = parser.getSupportedProtocol();
            parserMap.put(protocolType, parser);
            log.info("注册协议解析器: {} -> {}", protocolType, parser.getClass().getSimpleName());
        }
        log.info("协议解析器工厂初始化完成，共注册{}个解析器", parserMap.size());
    }

    /**
     * 根据协议类型获取解析器
     *
     * @param protocolType 协议类型
     * @return 解析器
     */
    public ProtocolParser<?> getParser(ProtocolType protocolType) {
        return parserMap.get(protocolType);
    }

    /**
     * 根据数据包自动选择解析器
     *
     * @param payload 数据包
     * @return 解析器
     */
    public ProtocolParser<?> selectParser(String payload) {
        for (ProtocolParser<?> parser : parserMap.values()) {
            if (parser.supports(payload)) {
                return parser;
            }
        }
        return null;
    }
}
```

### 4.3 具体解析器实现示例

```java
package com.memory.iot.protocol.parser;

import com.memory.iot.protocol.core.ProtocolParser;
import com.memory.iot.protocol.model.ParseResult;
import com.memory.iot.protocol.model.ProtocolType;
import com.memory.iot.protocol.dto.InsectKillerDataDTO;
import com.memory.iot.common.util.CommonUtil;
import com.memory.iot.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 杀虫灯协议解析器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class InsectKillerProtocolParser implements ProtocolParser<InsectKillerDataDTO> {

    private static final String CONTROL_CODE = "81";
    private static final String PROTOCOL_ID = "9070";

    @Override
    public ProtocolType getSupportedProtocol() {
        return ProtocolType.INSECT_KILLER;
    }

    @Override
    public boolean supports(String payload) {
        if (payload == null || payload.length() < 24) {
            return false;
        }

        try {
            int index = 14;
            String controlCode = payload.substring(index, index + 2);
            index += 6;
            String agreement = CommonUtil.reverse(payload.substring(index, index + 4));

            return CONTROL_CODE.equals(controlCode) && PROTOCOL_ID.equals(agreement);
        } catch (Exception e) {
            log.warn("检查杀虫灯协议支持时发生异常: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public ParseResult<InsectKillerDataDTO> parse(String payload) throws ProtocolParseException {
        try {
            log.info("开始解析杀虫灯数据: {}", payload);

            InsectKillerDataDTO data = parseInsectKillerData(payload);

            return ParseResult.<InsectKillerDataDTO>builder()
                    .success(true)
                    .data(data)
                    .protocolType(ProtocolType.INSECT_KILLER)
                    .rawPayload(payload)
                    .build();

        } catch (Exception e) {
            log.error("解析杀虫灯数据失败: {}", e.getMessage(), e);
            throw new ProtocolParseException("杀虫灯数据解析失败", e);
        }
    }

    private InsectKillerDataDTO parseInsectKillerData(String hexString) {
        // 具体解析逻辑实现
        InsectKillerDataDTO dto = new InsectKillerDataDTO();

        // 包头解析
        dto.setHeader(hexString.substring(0, 14));

        int index = 14;
        // 控制码
        dto.setControlCode(hexString.substring(index, index + 2));
        index += 2;

        // 数据长度
        Integer dataLength = Integer.parseInt(
            CommonUtil.reverse(hexString.substring(index, index + 4)), 16);
        dto.setDataLength(dataLength.toString());
        index += 4;

        // 验证数据长度
        int actualLength = hexString.length() - 24;
        if (dataLength * 2 != actualLength) {
            throw new IllegalArgumentException("数据长度与报文中指定的长度不匹配");
        }

        // 继续解析其他字段...
        // 这里省略具体的字段解析逻辑

        return dto;
    }
}
```

### 4.4 协议类型枚举

```java
package com.memory.iot.protocol.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 协议类型枚举
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Getter
@RequiredArgsConstructor
public enum ProtocolType {

    INSECT_KILLER("9070", "杀虫灯"),
    VALVE_CONTROLLER("9086", "智能阀控器"),
    GREENHOUSE_SENSOR("9003", "温室传感器"),
    ANALOG_MONITOR("9053", "模拟量监测"),
    SENSOR_DATA("9002", "传感器数据"),
    HISTORICAL_DATA("9013", "历史数据"),
    GPS_DATA("9005", "GPS数据");

    private final String protocolId;
    private final String description;

    /**
     * 根据协议ID获取协议类型
     *
     * @param protocolId 协议ID
     * @return 协议类型
     */
    public static ProtocolType fromProtocolId(String protocolId) {
        for (ProtocolType type : values()) {
            if (type.protocolId.equals(protocolId)) {
                return type;
            }
        }
        return null;
    }
}
```

### 4.5 功能完整性保障

#### 4.5.1 设备类型支持完整性检查

**现有11种设备类型重构映射**：

| 设备类型 | 协议标识 | 现有解析方法 | 重构后解析器 | 状态 |
|----------|----------|--------------|--------------|------|
| 智能控制器 | 9001 | CommonUtil.reply | IntelligentControllerParser | ✅保留 |
| 智能采集器 | 9002/9013 | CommonUtil.parseSensorData | IntelligentGathererParser | ✅保留 |
| 杀虫灯 | 9070 | DataParser.parse | InsectKillerParser | ✅保留 |
| 智能阀控器 | 9086 | DataParser.readValveDateParse | ValveControllerParser | ✅保留 |
| 温室大棚 | 9003 | DataParser.readGreenhouseSensorsParse | GreenhouseParser | ✅保留 |
| 气象站 | 9005 | CommonUtil.parseSensorData | WeatherStationParser | ✅保留 |
| 智能采集控制器 | 9053 | DataParser.readAnalogMonitorParse | AnalogMonitorParser | ✅保留 |
| 虫情设备 | 8070 | DataParser.inseckKillReadWorkParse | PestMonitorParser | ✅保留 |
| 虫情测报灯 | 自定义 | 扩展解析 | PestReportParser | ✅保留 |
| 视频监控 | 自定义 | 扩展解析 | VideoMonitorParser | ✅保留 |
| 水肥控制 | 801C/801D | DataParser相关方法 | FertilizerControlParser | ✅保留 |

#### 4.5.2 协议解析功能迁移方案

```java
package com.memory.iot.protocol.parser;

import com.memory.iot.protocol.core.ProtocolParser;
import com.memory.iot.protocol.model.ParseResult;
import com.memory.iot.protocol.model.ProtocolType;
import com.memory.iot.protocol.dto.SensorDataDTO;
import com.memory.iot.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 智能采集器协议解析器 - 支持9002/9013协议
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class IntelligentGathererParser implements ProtocolParser<SensorDataDTO> {

    @Override
    public ProtocolType getSupportedProtocol() {
        return ProtocolType.INTELLIGENT_GATHERER;
    }

    @Override
    public boolean supports(String payload) {
        if (payload == null || payload.length() < 24) {
            return false;
        }

        try {
            // 复用现有CommonUtil.divideHeader逻辑
            var headerMap = parseHeader(payload);
            String functionCode = headerMap.get("function-code");
            return "9002".equals(functionCode) || "9013".equals(functionCode);
        } catch (Exception e) {
            log.warn("检查智能采集器协议支持时发生异常: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public ParseResult<SensorDataDTO> parse(String payload) throws ProtocolParseException {
        try {
            log.info("开始解析智能采集器数据: {}", payload);

            // 复用现有CommonUtil.parseSensorData逻辑
            var sensorData = parseSensorDataFromPayload(payload);

            return ParseResult.<SensorDataDTO>builder()
                    .success(true)
                    .data(sensorData)
                    .protocolType(ProtocolType.INTELLIGENT_GATHERER)
                    .rawPayload(payload)
                    .build();

        } catch (Exception e) {
            log.error("解析智能采集器数据失败: {}", e.getMessage(), e);
            throw new ProtocolParseException("智能采集器数据解析失败", e);
        }
    }

    private SensorDataDTO parseSensorDataFromPayload(String payload) {
        // 这里复用现有的CommonUtil.parseSensorData逻辑
        // 保证解析结果与现有系统完全一致
        return new SensorDataDTO(); // 实际实现
    }

    private Map<String, String> parseHeader(String payload) {
        // 复用现有的CommonUtil.divideHeader逻辑
        return new HashMap<>(); // 实际实现
    }
}
```

#### 4.5.3 核心功能连续性保障

**设备认证功能**：
```java
package com.memory.iot.service.auth;

import com.memory.iot.repository.redis.RedisRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 设备认证服务 - 保持现有认证逻辑
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceAuthService {

    private final RedisRepository redisRepository;

    /**
     * 设备认证 - 复用现有逻辑
     * 格式：*productCode#deviceNo#script#
     */
    public boolean authenticateDevice(String authPayload) {
        try {
            // 保持现有认证逻辑不变
            if (!authPayload.contains("*") || !authPayload.contains("#")) {
                return false;
            }

            String cleanPayload = authPayload.replaceAll("\\*", "");
            String[] parts = cleanPayload.split("#");

            if (parts.length < 3) {
                return false;
            }

            String productCode = parts[0];
            String deviceNo = reverseHex(parts[1]); // 复用CommonUtil.reverse逻辑
            String script = reverseHex(parts[2]);

            // 检查Redis中的认证码
            String cachedCode = redisRepository.getDeviceAuthCode(deviceNo);
            return cachedCode != null && !cachedCode.isEmpty();

        } catch (Exception e) {
            log.error("设备认证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    private String reverseHex(String hex) {
        // 复用CommonUtil.reverse逻辑
        if (hex.length() % 2 != 0) hex = "0" + hex;
        char[] chars = hex.toCharArray();
        StringBuilder result = new StringBuilder();
        for (int i = chars.length; i > 0; i -= 2) {
            result.append(chars[i - 2]);
            result.append(chars[i - 1]);
        }
        return result.toString();
    }
}
```

**心跳检测功能**：
```java
package com.memory.iot.service.device;

import com.memory.iot.network.manager.ConnectionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 心跳检测服务 - 保持现有检测逻辑
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HeartbeatService {

    private final ConnectionManager connectionManager;

    /**
     * 定时清理超时设备 - 保持现有30分钟检测，3小时超时逻辑
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void cleanupTimeoutDevices() {
        log.info("开始清理超时设备连接...");

        long currentTime = System.currentTimeMillis() / 1000;
        long timeoutThreshold = 3 * 60 * 60; // 3小时

        connectionManager.getAllConnections().forEach((deviceNo, clientInfo) -> {
            try {
                if (Math.abs(currentTime - clientInfo.getUnixtime()) > timeoutThreshold) {
                    log.info("设备{}连接超时，执行清理", deviceNo);
                    connectionManager.closeConnection(deviceNo, "heartbeat_timeout");
                }
            } catch (Exception e) {
                log.error("清理设备{}连接时发生异常: {}", deviceNo, e.getMessage(), e);
            }
        });
    }
}
```

#### 4.5.4 HTTP API兼容性保障

**现有API接口完整保留**：

```java
package com.memory.iot.web.controller;

import com.memory.iot.service.command.CommandService;
import com.memory.iot.service.device.DeviceService;
import com.memory.iot.web.dto.CmdRequest;
import com.memory.iot.web.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * API控制器 - 保持现有接口完全兼容
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class ApiController {

    private final CommandService commandService;
    private final DeviceService deviceService;

    /**
     * 即时命令下发 - 保持现有接口签名
     */
    @PostMapping(value = "/cmd/1")
    public String sendImmediateCommand(@RequestBody CmdRequest request) {
        log.info("即时命令下发: {}", request);

        if (request == null || !isValidRequest(request)) {
            return "ERROR";
        }

        Map<String, Object> result = commandService.sendImmediateCommand(
            request.getDeviceNo(), request.getCmd());
        return result.get("message").toString();
    }

    /**
     * 缓存命令下发 - 保持现有接口签名
     */
    @PostMapping(value = "/cmd/2")
    public String sendCachedCommand(@RequestBody CmdRequest request) {
        log.info("缓存命令下发: {}", request);

        if (request == null || !isValidRequest(request)) {
            return "ERROR";
        }

        boolean success = commandService.cacheCommand(
            request.getDeviceNo(), request.getCmd());
        return success ? "SUCCESS" : "ERROR";
    }

    /**
     * 智能命令下发 - 保持现有接口签名
     */
    @PostMapping(value = "/cmd/3")
    public String sendSmartCommand(@RequestBody CmdRequest request) {
        log.info("智能命令下发: {}", request);

        // 先尝试即时命令
        String result = sendImmediateCommand(request);

        // 如果设备离线，则缓存命令
        if ("OFFLINE".equals(result)) {
            result = sendCachedCommand(request);
        }

        return result;
    }

    /**
     * 带UUID的智能命令下发 - 保持现有接口签名
     */
    @PostMapping(value = "/cmd/3/uuid")
    public Map<String, Object> sendSmartCommandWithUuid(@RequestBody CmdRequest request) {
        log.info("带UUID的智能命令下发: {}", request);
        return commandService.sendCommandWithUuid(request.getDeviceNo(), request.getCmd());
    }

    /**
     * 查询缓存命令列表 - 保持现有接口签名
     */
    @GetMapping(value = "/cmd/2/list")
    public List<String> getCachedCommands(@RequestParam String deviceNo) {
        if (deviceNo == null || deviceNo.trim().isEmpty()) {
            return null;
        }
        return commandService.getCachedCommands(deviceNo);
    }

    /**
     * 查询推送队列 - 保持现有接口签名
     */
    @GetMapping(value = "/queue/list")
    public List<String> getPushQueue() {
        return deviceService.getPushQueueData();
    }

    /**
     * 查询设备镜像信息 - 保持现有接口签名
     */
    @GetMapping(value = "/mirror")
    public Object getDeviceMirror(@RequestParam String deviceNo) {
        return deviceService.getDeviceMirrorInfo(deviceNo);
    }

    /**
     * 关闭设备连接 - 保持现有接口签名
     */
    @GetMapping(value = "/mirror/close")
    public String closeDeviceConnection(@RequestParam String deviceNo) {
        boolean success = deviceService.closeDeviceConnection(deviceNo);
        return success ? "SUCCESS" : "ERROR";
    }

    private boolean isValidRequest(CmdRequest request) {
        return request.getCmd() != null && !request.getCmd().trim().isEmpty() &&
               request.getDeviceNo() != null && !request.getDeviceNo().trim().isEmpty();
    }
}
```

**请求DTO保持兼容**：

```java
package com.memory.iot.web.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 命令请求DTO - 保持与现有CmdVo完全兼容
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
public class CmdRequest {

    @JsonProperty("deviceNo")
    private String deviceNo;

    @JsonProperty("cmd")
    private String cmd;

    @JsonProperty("uuid")
    private String uuid;

    // 保持toString方法用于日志输出
    @Override
    public String toString() {
        return "CmdRequest{" +
                "deviceNo='" + deviceNo + '\'' +
                ", cmd='" + cmd + '\'' +
                ", uuid='" + uuid + '\'' +
                '}';
    }
}
```

## 5. 代码结构优化

### 5.1 新包结构设计

```
com.memory.iot
├── common                          # 公共模块
│   ├── constant                    # 常量定义
│   ├── exception                   # 异常定义
│   ├── util                        # 工具类
│   └── config                      # 配置类
├── protocol                        # 协议解析模块
│   ├── core                        # 核心接口
│   ├── factory                     # 工厂类
│   ├── parser                      # 解析器实现
│   ├── model                       # 协议模型
│   └── dto                         # 数据传输对象
├── network                         # 网络通信模块
│   ├── server                      # 服务器实现
│   ├── handler                     # 处理器
│   ├── codec                       # 编解码器
│   └── manager                     # 连接管理
├── service                         # 业务服务模块
│   ├── device                      # 设备服务
│   ├── command                     # 命令服务
│   ├── auth                        # 认证服务
│   └── notification                # 通知服务
├── repository                      # 数据访问模块
│   ├── redis                       # Redis操作
│   ├── tdengine                    # TDengine操作
│   └── model                       # 数据模型
└── web                            # Web接口模块
    ├── controller                  # 控制器
    ├── dto                         # 请求响应DTO
    ├── exception                   # 异常处理
    └── config                      # Web配置
```

### 5.2 统一异常处理

```java
package com.memory.iot.common.exception;

import lombok.Getter;

/**
 * 业务异常基类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Getter
public class IotBusinessException extends RuntimeException {

    private final String errorCode;
    private final String errorMessage;

    public IotBusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode.getCode();
        this.errorMessage = errorCode.getMessage();
    }

    public IotBusinessException(ErrorCode errorCode, String customMessage) {
        super(customMessage);
        this.errorCode = errorCode.getCode();
        this.errorMessage = customMessage;
    }

    public IotBusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode.getCode();
        this.errorMessage = errorCode.getMessage();
    }
}
```

```java
package com.memory.iot.common.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 错误码枚举
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Getter
@RequiredArgsConstructor
public enum ErrorCode {

    // 系统级错误 (10000-19999)
    SYSTEM_ERROR("10000", "系统内部错误"),
    PARAM_INVALID("10001", "参数校验失败"),

    // 设备相关错误 (20000-29999)
    DEVICE_NOT_FOUND("20000", "设备不存在"),
    DEVICE_OFFLINE("20001", "设备离线"),
    DEVICE_AUTH_FAILED("20002", "设备认证失败"),

    // 协议相关错误 (30000-39999)
    PROTOCOL_PARSE_ERROR("30000", "协议解析失败"),
    PROTOCOL_NOT_SUPPORTED("30001", "不支持的协议类型"),

    // 命令相关错误 (40000-49999)
    COMMAND_SEND_FAILED("40000", "命令发送失败"),
    COMMAND_TIMEOUT("40001", "命令执行超时"),

    // 数据存储错误 (50000-59999)
    DATA_SAVE_FAILED("50000", "数据保存失败"),
    CACHE_ERROR("50001", "缓存操作失败");

    private final String code;
    private final String message;
}
```

### 5.3 全局异常处理器

```java
package com.memory.iot.web.exception;

import com.memory.iot.common.exception.IotBusinessException;
import com.memory.iot.web.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 业务异常处理
     */
    @ExceptionHandler(IotBusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleBusinessException(IotBusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return ApiResponse.error(e.getErrorCode(), e.getErrorMessage());
    }

    /**
     * 系统异常处理
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return ApiResponse.error("10000", "系统内部错误");
    }
}
```

## 6. 数据存储架构优化

### 6.1 TDengine存储模型优化

#### 6.1.1 当前存储模型分析
- **现状**：每个设备一个子表 (`sensor_{deviceNo}`)
- **问题**：设备数量增长时表数量激增，影响查询性能
- **优化方案**：按设备类型分表，提高查询效率

#### 6.1.2 新存储模型设计

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS iot_data;

-- 使用数据库
USE iot_data;

-- 按设备类型创建超级表
CREATE STABLE IF NOT EXISTS insect_killer_data (
    ts TIMESTAMP,
    device_no NCHAR(32),
    air_temperature FLOAT,
    air_humidity FLOAT,
    solar_voltage FLOAT,
    power_voltage FLOAT,
    network_status INT,
    signal_strength INT,
    device_status NCHAR(10),
    raw_data NCHAR(2048)
) TAGS (
    device_type NCHAR(32),
    location NCHAR(64),
    install_date TIMESTAMP
);

CREATE STABLE IF NOT EXISTS valve_controller_data (
    ts TIMESTAMP,
    device_no NCHAR(32),
    valve_status INT,
    control_mode INT,
    preset_opening INT,
    actual_opening INT,
    flow_rate FLOAT,
    pressure FLOAT,
    raw_data NCHAR(2048)
) TAGS (
    device_type NCHAR(32),
    location NCHAR(64),
    install_date TIMESTAMP
);

CREATE STABLE IF NOT EXISTS greenhouse_sensor_data (
    ts TIMESTAMP,
    device_no NCHAR(32),
    sensor_count INT,
    temperature FLOAT,
    humidity FLOAT,
    light_intensity FLOAT,
    co2_concentration FLOAT,
    soil_moisture FLOAT,
    raw_data NCHAR(2048)
) TAGS (
    device_type NCHAR(32),
    greenhouse_id NCHAR(32),
    install_date TIMESTAMP
);
```

#### 6.1.3 数据访问层重构

```java
package com.memory.iot.repository.tdengine;

import com.memory.iot.repository.model.SensorData;
import com.memory.iot.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;

/**
 * TDengine数据访问实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TDengineRepository {

    private final DataSource tdengineDataSource;

    /**
     * 保存传感器数据
     *
     * @param sensorData 传感器数据
     */
    public void saveSensorData(SensorData sensorData) {
        String tableName = getTableName(sensorData.getDeviceType());
        String sql = buildInsertSql(tableName, sensorData);

        try (Connection conn = tdengineDataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            setStatementParameters(stmt, sensorData);
            stmt.executeUpdate();

            log.debug("保存传感器数据成功: deviceNo={}, type={}",
                     sensorData.getDeviceNo(), sensorData.getDeviceType());

        } catch (SQLException e) {
            log.error("保存传感器数据失败: deviceNo={}, error={}",
                     sensorData.getDeviceNo(), e.getMessage(), e);
            throw new DataAccessException("保存传感器数据失败", e);
        }
    }

    private String getTableName(String deviceType) {
        return switch (deviceType.toLowerCase()) {
            case "insect_killer" -> "insect_killer_data";
            case "valve_controller" -> "valve_controller_data";
            case "greenhouse_sensor" -> "greenhouse_sensor_data";
            default -> "general_sensor_data";
        };
    }

    private String buildInsertSql(String tableName, SensorData sensorData) {
        return String.format(
            "INSERT INTO %s_%s USING %s TAGS (?, ?, ?) VALUES (NOW, ?, ?)",
            tableName, sensorData.getDeviceNo(), tableName
        );
    }

    private void setStatementParameters(PreparedStatement stmt, SensorData sensorData)
            throws SQLException {
        // 设置TAGS参数
        stmt.setString(1, sensorData.getDeviceType());
        stmt.setString(2, sensorData.getLocation());
        stmt.setTimestamp(3, java.sql.Timestamp.valueOf(sensorData.getInstallDate()));

        // 设置VALUES参数
        stmt.setString(4, sensorData.getDeviceNo());
        stmt.setString(5, JsonUtil.toJsonString(sensorData.getData()));
    }
}
```

### 6.2 Redis缓存策略优化

#### 6.2.1 缓存键命名规范

```java
package com.memory.iot.common.constant;

/**
 * Redis键常量
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public final class RedisKeyConstants {

    private RedisKeyConstants() {}

    // 键前缀
    private static final String IOT_PREFIX = "iot:";

    // 设备相关
    public static final String DEVICE_CACHE = IOT_PREFIX + "device:cache:";
    public static final String DEVICE_STATUS = IOT_PREFIX + "device:status:";
    public static final String DEVICE_AUTH = IOT_PREFIX + "device:auth:";

    // 命令相关
    public static final String COMMAND_QUEUE = IOT_PREFIX + "command:queue:";
    public static final String COMMAND_RESULT = IOT_PREFIX + "command:result:";

    // 数据推送
    public static final String PUSH_QUEUE = IOT_PREFIX + "push:queue";
    public static final String PUSH_FAILED = IOT_PREFIX + "push:failed";

    // 统计信息
    public static final String STATS_ONLINE_COUNT = IOT_PREFIX + "stats:online:count";
    public static final String STATS_MESSAGE_COUNT = IOT_PREFIX + "stats:message:count:";

    /**
     * 构建设备缓存键
     */
    public static String deviceCacheKey(String deviceNo) {
        return DEVICE_CACHE + deviceNo;
    }

    /**
     * 构建命令队列键
     */
    public static String commandQueueKey(String deviceNo) {
        return COMMAND_QUEUE + deviceNo;
    }
}
```

#### 6.2.2 Redis操作封装

```java
package com.memory.iot.repository.redis;

import com.memory.iot.common.constant.RedisKeyConstants;
import com.memory.iot.common.exception.CacheException;
import com.memory.iot.repository.model.DeviceStatus;
import com.memory.iot.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis操作封装
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class RedisRepository {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 保存设备状态
     *
     * @param deviceNo 设备编号
     * @param status 设备状态
     */
    public void saveDeviceStatus(String deviceNo, DeviceStatus status) {
        try {
            String key = RedisKeyConstants.deviceCacheKey(deviceNo);
            Map<String, Object> statusMap = Map.of(
                "onlineTime", status.getOnlineTime(),
                "offlineTime", status.getOfflineTime(),
                "heartTime", status.getHeartTime(),
                "status", status.getStatus(),
                "lastUpdateTime", System.currentTimeMillis()
            );

            redisTemplate.opsForHash().putAll(key, statusMap);
            redisTemplate.expire(key, Duration.ofHours(24));

            log.debug("保存设备状态成功: deviceNo={}", deviceNo);
        } catch (Exception e) {
            log.error("保存设备状态失败: deviceNo={}, error={}", deviceNo, e.getMessage(), e);
            throw new CacheException("保存设备状态失败", e);
        }
    }

    /**
     * 获取设备状态
     *
     * @param deviceNo 设备编号
     * @return 设备状态
     */
    public DeviceStatus getDeviceStatus(String deviceNo) {
        try {
            String key = RedisKeyConstants.deviceCacheKey(deviceNo);
            Map<Object, Object> statusMap = redisTemplate.opsForHash().entries(key);

            if (statusMap.isEmpty()) {
                return null;
            }

            return DeviceStatus.builder()
                .deviceNo(deviceNo)
                .onlineTime((String) statusMap.get("onlineTime"))
                .offlineTime((String) statusMap.get("offlineTime"))
                .heartTime((String) statusMap.get("heartTime"))
                .status((Integer) statusMap.get("status"))
                .build();

        } catch (Exception e) {
            log.error("获取设备状态失败: deviceNo={}, error={}", deviceNo, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 添加命令到队列
     *
     * @param deviceNo 设备编号
     * @param command 命令内容
     */
    public void pushCommand(String deviceNo, String command) {
        try {
            String key = RedisKeyConstants.commandQueueKey(deviceNo);
            redisTemplate.opsForList().rightPush(key, command);
            redisTemplate.expire(key, Duration.ofHours(1));

            log.debug("添加命令到队列: deviceNo={}, command={}", deviceNo, command);
        } catch (Exception e) {
            log.error("添加命令到队列失败: deviceNo={}, error={}", deviceNo, e.getMessage(), e);
            throw new CacheException("添加命令到队列失败", e);
        }
    }

    /**
     * 从队列获取命令
     *
     * @param deviceNo 设备编号
     * @return 命令列表
     */
    public List<String> popCommands(String deviceNo) {
        try {
            String key = RedisKeyConstants.commandQueueKey(deviceNo);
            return redisTemplate.opsForList().range(key, 0, -1);
        } catch (Exception e) {
            log.error("从队列获取命令失败: deviceNo={}, error={}", deviceNo, e.getMessage(), e);
            return List.of();
        }
    }
}
```

### 6.3 Jackson迁移详细步骤

#### 6.3.1 迁移检查清单

**第一步：依赖替换**
```bash
# 1. 移除FastJSON2依赖
# 在pom.xml中注释或删除：
# <dependency>
#     <groupId>com.alibaba.fastjson2</groupId>
#     <artifactId>fastjson2</artifactId>
# </dependency>

# 2. 添加Jackson依赖（参考2.2节配置）

# 3. 验证依赖冲突
mvn dependency:tree | grep -i json
```

**第二步：代码迁移**
```java
// 迁移前（FastJSON2）
import com.alibaba.fastjson2.JSONObject;

// 数据序列化
String json = JSONObject.toJSONString(data);

// 数据反序列化
SensorData data = JSONObject.parseObject(json, SensorData.class);

// 迁移后（Jackson）
import com.memory.iot.common.util.JsonUtil;

// 数据序列化
String json = JsonUtil.toJsonString(data);

// 数据反序列化
SensorData data = JsonUtil.parseObject(json, SensorData.class);
```

**第三步：DTO类注解迁移**
```java
// 迁移前
import com.alibaba.fastjson2.annotation.JSONField;

public class InsectKillerDataDTO {
    @JSONField(name = "device_no")
    private String deviceNo;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dateTime;
}

// 迁移后
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

public class InsectKillerDataDTO {
    @JsonProperty("device_no")
    private String deviceNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dateTime;
}
```

**第四步：测试验证**
```java
package com.memory.iot.common.util;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * Jackson迁移验证测试
 */
@SpringBootTest
class JsonUtilMigrationTest {

    @Test
    void shouldSerializeObjectCorrectly() {
        // Given
        TestData data = new TestData();
        data.setDeviceNo("TEST001");
        data.setTimestamp(LocalDateTime.now());

        // When
        String json = JsonUtil.toJsonString(data);

        // Then
        assertThat(json).isNotNull();
        assertThat(json).contains("TEST001");
    }

    @Test
    void shouldDeserializeObjectCorrectly() {
        // Given
        String json = "{\"device_no\":\"TEST001\",\"timestamp\":\"2024-01-01 10:00:00\"}";

        // When
        TestData data = JsonUtil.parseObject(json, TestData.class);

        // Then
        assertThat(data).isNotNull();
        assertThat(data.getDeviceNo()).isEqualTo("TEST001");
    }

    @Test
    void shouldParseToMapCorrectly() {
        // Given
        String json = "{\"deviceNo\":\"TEST001\",\"status\":1}";

        // When
        Map<String, Object> map = JsonUtil.parseToMap(json);

        // Then
        assertThat(map).isNotNull();
        assertThat(map.get("deviceNo")).isEqualTo("TEST001");
        assertThat(map.get("status")).isEqualTo(1);
    }
}
```

#### 6.3.2 迁移注意事项

**性能对比**：
- **序列化性能**：Jackson略慢于FastJSON2（约10-15%），但在可接受范围内
- **反序列化性能**：Jackson与FastJSON2基本持平
- **内存使用**：Jackson内存使用更稳定，GC压力更小

**兼容性问题**：
- **日期格式**：Jackson默认ISO-8601格式，需要配置为与FastJSON2一致
- **null值处理**：Jackson默认序列化null值，可配置忽略
- **数字精度**：Jackson对BigDecimal处理更严格

**安全性提升**：
- **autoType禁用**：Jackson默认禁用自动类型推断，避免反序列化攻击
- **白名单机制**：支持类型白名单，进一步提升安全性
- **CVE修复**：Jackson社区响应CVE更及时

## 7. 配置管理方案

### 7.1 Spring Boot配置文件结构

#### 7.1.1 主配置文件 (application.yml)

```yaml
# 应用基础配置
spring:
  application:
    name: iot-data-receiver
  profiles:
    active: @spring.profiles.active@

  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 2
      maximum-pool-size: 10
      connection-timeout: 30000
      max-lifetime: 0
      idle-timeout: 0
      connection-test-query: "SELECT 1"

  # Redis配置
  data:
    redis:
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /iot
  tomcat:
    threads:
      max: 200
      min-spare: 10

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.memory.iot: INFO
    org.springframework: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/iot-data-receiver.log
    max-size: 100MB
    max-history: 30

# IoT系统配置
iot:
  network:
    tcp:
      port: 32080
      boss-threads: 1
      worker-threads: 0  # 0表示使用CPU核心数
      so-backlog: 128
      keep-alive: true
      tcp-nodelay: true

  protocol:
    timeout: 30000
    max-frame-length: 8192

  device:
    heartbeat-interval: 60
    offline-timeout: 180
    cleanup-interval: 1800

  notification:
    push-url: ${IOT_PUSH_URL:http://localhost:8081/api/data/receive}
    retry-times: 3
    retry-interval: 5000
    batch-size: 100
```

#### 7.1.2 开发环境配置 (application-dev.yml)

```yaml
# 开发环境配置
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 7
      password:

  datasource:
    url: **********************************
    username: root
    password: taosdata
    driver-class-name: com.taosdata.jdbc.TSDBDriver

# 开发环境日志级别
logging:
  level:
    com.memory.iot: DEBUG
    org.springframework.data.redis: DEBUG

# 开发环境IoT配置
iot:
  network:
    tcp:
      port: 32080
  notification:
    push-url: http://localhost:8081/api/data/receive
    enabled: false  # 开发环境禁用推送

# 开发工具配置
spring:
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true
```

#### 7.1.3 生产环境配置 (application-prod.yml)

```yaml
# 生产环境配置
spring:
  data:
    redis:
      host: ${REDIS_HOST:redis-cluster}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DB:0}
      password: ${REDIS_PASSWORD:}
      cluster:
        nodes: ${REDIS_CLUSTER_NODES:}
        max-redirects: 3

  datasource:
    url: jdbc:TAOS://${TDENGINE_HOST:tdengine}:${TDENGINE_PORT:6030}/${TDENGINE_DB:iot_prod}
    username: ${TDENGINE_USER:root}
    password: ${TDENGINE_PASSWORD:taosdata}
    driver-class-name: com.taosdata.jdbc.TSDBDriver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

# 生产环境日志配置
logging:
  level:
    com.memory.iot: INFO
    org.springframework: WARN
  file:
    name: /app/logs/iot-data-receiver.log

# 生产环境IoT配置
iot:
  network:
    tcp:
      port: ${IOT_TCP_PORT:32080}
      worker-threads: ${IOT_WORKER_THREADS:0}

  notification:
    push-url: ${IOT_PUSH_URL}
    enabled: true
    retry-times: 5
```

### 7.2 配置属性类定义

```java
package com.memory.iot.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * IoT系统配置属性
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "iot")
public class IotProperties {

    /**
     * 网络配置
     */
    private Network network = new Network();

    /**
     * 协议配置
     */
    private Protocol protocol = new Protocol();

    /**
     * 设备配置
     */
    private Device device = new Device();

    /**
     * 通知配置
     */
    private Notification notification = new Notification();

    @Data
    public static class Network {
        private Tcp tcp = new Tcp();

        @Data
        public static class Tcp {
            private int port = 32080;
            private int bossThreads = 1;
            private int workerThreads = 0;
            private int soBacklog = 128;
            private boolean keepAlive = true;
            private boolean tcpNodelay = true;
        }
    }

    @Data
    public static class Protocol {
        private int timeout = 30000;
        private int maxFrameLength = 8192;
    }

    @Data
    public static class Device {
        private int heartbeatInterval = 60;
        private int offlineTimeout = 180;
        private int cleanupInterval = 1800;
    }

    @Data
    public static class Notification {
        private String pushUrl;
        private boolean enabled = true;
        private int retryTimes = 3;
        private int retryInterval = 5000;
        private int batchSize = 100;
    }
}
```

## 8. 实施计划

### 8.1 分阶段重构计划

#### 第一阶段：基础架构重构 (2周)

**目标**：建立新的模块结构和基础框架

**任务清单**：
1. **Maven模块重构** (3天)
   - 创建新的模块结构
   - 更新依赖版本
   - 配置模块间依赖关系

2. **公共组件开发** (4天)
   - 异常处理体系
   - 工具类重构
   - 常量定义规范化

3. **配置管理实现** (3天)
   - 配置属性类开发
   - 环境配置文件编写
   - 配置验证机制

4. **基础测试框架** (4天)
   - 单元测试基础设施
   - 集成测试环境搭建
   - 测试数据准备

**里程碑**：基础框架搭建完成，可以启动应用

#### 第二阶段：协议解析框架重构 (3周)

**目标**：实现可扩展的协议解析框架，确保11种设备类型100%兼容

**任务清单**：
1. **协议解析接口设计** (3天)
   - ProtocolParser接口定义
   - ParseResult模型设计
   - 异常处理机制

2. **协议工厂实现** (4天)
   - ProtocolParserFactory开发
   - 解析器注册机制
   - 自动发现功能

3. **具体解析器迁移** (10天) - **功能完整性保障**
   - 杀虫灯解析器 (协议9070) (1天)
   - 智能阀控器解析器 (协议9086) (1天)
   - 温室传感器解析器 (协议9003) (1天)
   - 模拟量监测解析器 (协议9053) (1天)
   - 智能采集器解析器 (协议9002/9013) (2天)
   - 水肥控制解析器 (协议801C/801D) (1天)
   - 虫情设备解析器 (协议8070) (1天)
   - 气象站解析器 (协议9005) (1天)
   - 其他设备类型解析器 (1天)

4. **解析器测试和验证** (4天)
   - 单元测试编写
   - 历史数据包兼容性测试
   - 性能基准测试
   - **功能回归测试**

**里程碑**：所有11种设备类型解析器迁移完成，功能验证100%通过

#### 第三阶段：网络通信层重构 (2周)

**目标**：优化网络通信和连接管理

**任务清单**：
1. **Netty服务器重构** (4天)
   - 服务器启动逻辑优化
   - 连接管理器实现
   - 心跳检测机制

2. **数据编解码优化** (3天)
   - PackageDecoder重构
   - 协议识别优化
   - 错误处理增强

3. **连接状态管理** (4天)
   - ClientInfo模型优化
   - 连接池管理
   - 状态同步机制

4. **网络层测试** (3天)
   - 连接测试
   - 并发测试
   - 稳定性测试

**里程碑**：网络通信层重构完成，连接稳定性提升

#### 第四阶段：业务服务层重构 (2周)

**目标**：实现清晰的业务逻辑分层

**任务清单**：
1. **服务接口设计** (3天)
   - DeviceService接口
   - CommandService接口
   - AuthService接口
   - NotificationService接口

2. **服务实现开发** (7天)
   - 设备管理服务 (2天)
   - 命令处理服务 (2天)
   - 认证服务 (1天)
   - 通知服务 (2天)

3. **服务集成测试** (4天)
   - 服务间调用测试
   - 事务处理测试
   - 异常场景测试

**里程碑**：业务服务层重构完成，业务逻辑清晰

#### 第五阶段：数据访问层优化 (2周)

**目标**：优化数据存储和访问性能

**任务清单**：
1. **TDengine存储优化** (5天)
   - 表结构重设计
   - 数据迁移脚本
   - 查询性能优化

2. **Redis缓存优化** (4天)
   - 缓存策略重设计
   - 键命名规范化
   - 过期策略优化

3. **Repository层实现** (3天)
   - 数据访问接口
   - 事务管理
   - 异常处理

4. **数据层测试** (2天)
   - 数据访问测试
   - 性能基准测试

**里程碑**：数据访问层优化完成，性能提升20%

#### 第六阶段：系统集成与部署 (1周)

**目标**：完成系统集成和部署准备

**任务清单**：
1. **系统集成测试** (3天)
   - 端到端功能测试
   - 性能压力测试
   - 稳定性测试

2. **部署配置** (2天)
   - Docker镜像构建
   - 部署脚本编写
   - 监控配置

3. **文档完善** (2天)
   - 部署文档
   - 运维手册
   - API文档

**里程碑**：系统重构完成，准备生产部署

### 8.2 时间估算和资源分配

| 阶段 | 工期 | 开发人员 | 测试人员 | 主要风险 |
|------|------|----------|----------|----------|
| 第一阶段 | 2周 | 2人 | 1人 | 依赖版本兼容性 |
| 第二阶段 | 3周 | 3人 | 1人 | 协议解析逻辑复杂 |
| 第三阶段 | 2周 | 2人 | 1人 | 网络稳定性 |
| 第四阶段 | 2周 | 2人 | 1人 | 业务逻辑理解 |
| 第五阶段 | 2周 | 2人 | 1人 | 数据迁移风险 |
| 第六阶段 | 1周 | 3人 | 2人 | 集成问题 |
| **总计** | **12周** | **2-3人** | **1-2人** | - |

## 9. 测试策略

### 9.1 单元测试方案

#### 9.1.1 测试框架配置

```xml
<!-- 测试依赖 -->
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>redis</artifactId>
        <scope>test</scope>
    </dependency>
</dependencies>
```

#### 9.1.2 协议解析器单元测试

```java
package com.memory.iot.protocol.parser;

import com.memory.iot.protocol.dto.InsectKillerDataDTO;
import com.memory.iot.protocol.model.ParseResult;
import com.memory.iot.protocol.model.ProtocolType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.*;

/**
 * 杀虫灯协议解析器单元测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("杀虫灯协议解析器测试")
class InsectKillerProtocolParserTest {

    private InsectKillerProtocolParser parser;

    @BeforeEach
    void setUp() {
        parser = new InsectKillerProtocolParser();
    }

    @Test
    @DisplayName("应该返回正确的协议类型")
    void shouldReturnCorrectProtocolType() {
        // When
        ProtocolType protocolType = parser.getSupportedProtocol();

        // Then
        assertThat(protocolType).isEqualTo(ProtocolType.INSECT_KILLER);
    }

    @Test
    @DisplayName("应该正确识别支持的协议")
    void shouldSupportValidProtocol() {
        // Given
        String validPayload = "68010000228068810500709070001122334455667788990011223344556677889900112233445566778899001122334455667788990016";

        // When
        boolean supports = parser.supports(validPayload);

        // Then
        assertThat(supports).isTrue();
    }

    @Test
    @DisplayName("应该拒绝不支持的协议")
    void shouldNotSupportInvalidProtocol() {
        // Given
        String invalidPayload = "68010000228068810500709080001122334455667788990016";

        // When
        boolean supports = parser.supports(invalidPayload);

        // Then
        assertThat(supports).isFalse();
    }

    @Test
    @DisplayName("应该正确解析有效数据包")
    void shouldParseValidPayload() {
        // Given
        String validPayload = "68010000228068810500709070001122334455667788990011223344556677889900112233445566778899001122334455667788990016";

        // When
        ParseResult<InsectKillerDataDTO> result = parser.parse(validPayload);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNotNull();
        assertThat(result.getProtocolType()).isEqualTo(ProtocolType.INSECT_KILLER);
        assertThat(result.getRawPayload()).isEqualTo(validPayload);

        InsectKillerDataDTO data = result.getData();
        assertThat(data.getHeader()).isEqualTo("68010000228068");
        assertThat(data.getControlCode()).isEqualTo("81");
    }
}
```

#### 9.1.3 功能完整性验证测试

```java
package com.memory.iot.protocol.compatibility;

import com.memory.iot.protocol.factory.ProtocolParserFactory;
import com.memory.iot.protocol.model.ProtocolType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * 功能完整性验证测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootTest
@DisplayName("功能完整性验证测试")
class FunctionalCompletenessTest {

    @Autowired
    private ProtocolParserFactory parserFactory;

    @Test
    @DisplayName("验证所有11种设备类型都有对应的解析器")
    void shouldHaveParserForAllDeviceTypes() {
        // Given - 现有系统支持的所有设备类型
        List<ProtocolType> requiredTypes = Arrays.asList(
            ProtocolType.INSECT_KILLER,           // 杀虫灯 (9070)
            ProtocolType.VALVE_CONTROLLER,        // 智能阀控器 (9086)
            ProtocolType.GREENHOUSE_SENSOR,       // 温室传感器 (9003)
            ProtocolType.ANALOG_MONITOR,          // 模拟量监测 (9053)
            ProtocolType.INTELLIGENT_GATHERER,    // 智能采集器 (9002/9013)
            ProtocolType.FERTILIZER_CONTROLLER,   // 水肥控制 (801C/801D)
            ProtocolType.PEST_MONITOR,            // 虫情设备 (8070)
            ProtocolType.WEATHER_STATION,         // 气象站 (9005)
            ProtocolType.INTELLIGENT_CONTROLLER,  // 智能控制器
            ProtocolType.VIDEO_MONITOR,           // 视频监控
            ProtocolType.PEST_REPORT_LAMP         // 虫情测报灯
        );

        // When & Then - 验证每种设备类型都有对应的解析器
        for (ProtocolType type : requiredTypes) {
            var parser = parserFactory.getParser(type);
            assertThat(parser)
                .withFailMessage("设备类型 %s 缺少对应的解析器", type)
                .isNotNull();
        }
    }

    @Test
    @DisplayName("验证所有协议标识都能正确识别")
    void shouldRecognizeAllProtocolIds() {
        // Given - 现有系统支持的所有协议标识
        List<String> testPayloads = Arrays.asList(
            "68010000228068810500709070001122334455667788990016", // 9070 - 杀虫灯
            "68010000228068810500869086001122334455667788990016", // 9086 - 阀控器
            "68010000228068810500039003001122334455667788990016", // 9003 - 温室传感器
            "68010000228068810500539053001122334455667788990016", // 9053 - 模拟量监测
            "68010000228068810500029002001122334455667788990016", // 9002 - 传感器数据
            "68010000228068810500139013001122334455667788990016", // 9013 - 历史数据
            "68010000228068810500059005001122334455667788990016"  // 9005 - GPS数据
        );

        // When & Then - 验证每个协议都能找到对应的解析器
        for (String payload : testPayloads) {
            var parser = parserFactory.selectParser(payload);
            assertThat(parser)
                .withFailMessage("协议数据包 %s 无法找到对应的解析器", payload)
                .isNotNull();
        }
    }

    @Test
    @DisplayName("验证HTTP API接口完整性")
    void shouldHaveAllRequiredApiEndpoints() {
        // 这里可以通过MockMvc测试所有API端点
        // 验证 /cmd/1, /cmd/2, /cmd/3, /cmd/3/uuid 等接口都存在
        // 具体实现略...
    }
}
```

### 9.2 集成测试方案

#### 9.2.1 Redis集成测试

```java
package com.memory.iot.repository.redis;

import com.memory.iot.repository.model.DeviceStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import static org.assertj.core.api.Assertions.*;

/**
 * Redis集成测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootTest
@Testcontainers
@DisplayName("Redis集成测试")
class RedisRepositoryIntegrationTest {

    @Container
    static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:7-alpine"))
            .withExposedPorts(6379);

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", redis::getFirstMappedPort);
    }

    @Autowired
    private RedisRepository redisRepository;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        redisRepository.cleanExpiredDeviceStatus(0);
    }

    @Test
    @DisplayName("应该能够保存和获取设备状态")
    void shouldSaveAndGetDeviceStatus() {
        // Given
        String deviceNo = "TEST_DEVICE_001";
        DeviceStatus status = DeviceStatus.builder()
            .deviceNo(deviceNo)
            .onlineTime("2024-01-01 10:00:00")
            .heartTime("2024-01-01 10:05:00")
            .status(1)
            .build();

        // When
        redisRepository.saveDeviceStatus(deviceNo, status);
        DeviceStatus retrievedStatus = redisRepository.getDeviceStatus(deviceNo);

        // Then
        assertThat(retrievedStatus).isNotNull();
        assertThat(retrievedStatus.getDeviceNo()).isEqualTo(deviceNo);
        assertThat(retrievedStatus.getOnlineTime()).isEqualTo("2024-01-01 10:00:00");
        assertThat(retrievedStatus.getStatus()).isEqualTo(1);
    }

    @Test
    @DisplayName("应该能够管理命令队列")
    void shouldManageCommandQueue() {
        // Given
        String deviceNo = "TEST_DEVICE_002";
        String command1 = "CMD_001";
        String command2 = "CMD_002";

        // When
        redisRepository.pushCommand(deviceNo, command1);
        redisRepository.pushCommand(deviceNo, command2);

        var commands = redisRepository.popCommands(deviceNo);

        // Then
        assertThat(commands).hasSize(2);
        assertThat(commands).containsExactly(command1, command2);
    }
}
```

### 9.3 性能测试方案

#### 9.3.1 协议解析性能测试

```java
package com.memory.iot.protocol.performance;

import com.memory.iot.protocol.parser.InsectKillerProtocolParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.util.StopWatch;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.*;

/**
 * 协议解析性能测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@DisplayName("协议解析性能测试")
class ProtocolParserPerformanceTest {

    private InsectKillerProtocolParser parser;
    private String testPayload;

    @BeforeEach
    void setUp() {
        parser = new InsectKillerProtocolParser();
        testPayload = "68010000228068810500709070001122334455667788990011223344556677889900112233445566778899001122334455667788990016";
    }

    @Test
    @DisplayName("单线程解析性能测试")
    void singleThreadParsePerformanceTest() {
        // Given
        int iterations = 10000;
        StopWatch stopWatch = new StopWatch();

        // When
        stopWatch.start();
        for (int i = 0; i < iterations; i++) {
            parser.parse(testPayload);
        }
        stopWatch.stop();

        // Then
        long totalTime = stopWatch.getTotalTimeMillis();
        double avgTime = (double) totalTime / iterations;

        System.out.printf("单线程解析 %d 次，总耗时: %d ms，平均耗时: %.2f ms%n",
                         iterations, totalTime, avgTime);

        // 性能要求：平均解析时间应小于1ms
        assertThat(avgTime).isLessThan(1.0);
    }

    @Test
    @DisplayName("多线程并发解析性能测试")
    void multiThreadParsePerformanceTest() throws InterruptedException {
        // Given
        int threadCount = 10;
        int iterationsPerThread = 1000;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        StopWatch stopWatch = new StopWatch();

        // When
        stopWatch.start();
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < iterationsPerThread; j++) {
                        parser.parse(testPayload);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(30, TimeUnit.SECONDS);
        stopWatch.stop();

        // Then
        long totalTime = stopWatch.getTotalTimeMillis();
        int totalIterations = threadCount * iterationsPerThread;
        double avgTime = (double) totalTime / totalIterations;

        System.out.printf("多线程解析 %d 次（%d线程），总耗时: %d ms，平均耗时: %.2f ms%n",
                         totalIterations, threadCount, totalTime, avgTime);

        // 性能要求：多线程环境下平均解析时间应小于2ms
        assertThat(avgTime).isLessThan(2.0);

        executor.shutdown();
    }
}
```

## 10. 部署方案

### 10.1 Docker化部署配置

#### 10.1.1 Dockerfile

```dockerfile
# 多阶段构建Dockerfile
FROM openjdk:21-jdk-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件
COPY pom.xml .
COPY */pom.xml ./*/

# 复制源代码
COPY src ./src
COPY */src ./*/src

# 安装Maven
RUN apt-get update && apt-get install -y maven

# 构建应用
RUN mvn clean package -DskipTests

# 运行时镜像
FROM openjdk:21-jre-slim

# 创建应用用户
RUN groupadd -r iotapp && useradd -r -g iotapp iotapp

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

# 复制应用JAR文件
COPY --from=builder /app/boot/target/iot-data-receiver-*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs && chown -R iotapp:iotapp /app

# 切换到应用用户
USER iotapp

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/iot/actuator/health || exit 1

# 暴露端口
EXPOSE 8080 32080

# JVM参数优化
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 10.1.2 docker-compose.yml

```yaml
version: '3.8'

services:
  # IoT数据接收服务
  iot-data-receiver:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: iot-data-receiver
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "32080:32080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - TDENGINE_HOST=tdengine
      - TDENGINE_PORT=6030
      - TDENGINE_DB=iot_prod
      - TDENGINE_USER=root
      - TDENGINE_PASSWORD=${TDENGINE_PASSWORD:-taosdata}
      - IOT_PUSH_URL=${IOT_PUSH_URL}
      - IOT_TCP_PORT=32080
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
      - tdengine
    networks:
      - iot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/iot/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: iot-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - iot-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TDengine时序数据库
  tdengine:
    image: tdengine/tdengine:*******
    container_name: iot-tdengine
    restart: unless-stopped
    ports:
      - "6030:6030"
      - "6041:6041"
      - "6043-6049:6043-6049"
      - "6043-6049:6043-6049/udp"
    environment:
      - TAOS_FQDN=tdengine
    volumes:
      - tdengine-data:/var/lib/taos
      - tdengine-log:/var/log/taos
      - ./tdengine/taos.cfg:/etc/taos/taos.cfg
    networks:
      - iot-network
    healthcheck:
      test: ["CMD", "taos", "-s", "show databases;"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: iot-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - iot-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: iot-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - iot-network

volumes:
  redis-data:
  tdengine-data:
  tdengine-log:
  prometheus-data:
  grafana-data:

networks:
  iot-network:
    driver: bridge
```

### 10.2 监控配置

#### 10.2.1 Prometheus配置 (monitoring/prometheus.yml)

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'iot-data-receiver'
    static_configs:
      - targets: ['iot-data-receiver:8080']
    metrics_path: '/iot/actuator/prometheus'
    scrape_interval: 10s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'tdengine'
    static_configs:
      - targets: ['tdengine:6041']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 10.2.2 应用监控指标

```java
package com.memory.iot.common.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * IoT系统监控指标
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
public class IotMetrics {

    private final MeterRegistry meterRegistry;

    // 设备连接数
    private final AtomicLong connectedDevices = new AtomicLong(0);

    // 消息处理计数器
    private final Counter messageProcessedCounter;

    // 消息处理失败计数器
    private final Counter messageFailedCounter;

    // 协议解析时间
    private final Timer protocolParseTimer;

    // 数据存储时间
    private final Timer dataStorageTimer;

    public IotMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 注册指标
        this.messageProcessedCounter = Counter.builder("iot.message.processed")
            .description("处理的消息总数")
            .register(meterRegistry);

        this.messageFailedCounter = Counter.builder("iot.message.failed")
            .description("处理失败的消息总数")
            .register(meterRegistry);

        this.protocolParseTimer = Timer.builder("iot.protocol.parse.duration")
            .description("协议解析耗时")
            .register(meterRegistry);

        this.dataStorageTimer = Timer.builder("iot.data.storage.duration")
            .description("数据存储耗时")
            .register(meterRegistry);

        // 注册设备连接数Gauge
        Gauge.builder("iot.device.connected")
            .description("当前连接的设备数")
            .register(meterRegistry, this, IotMetrics::getConnectedDeviceCount);
    }

    public void incrementConnectedDevices() {
        connectedDevices.incrementAndGet();
    }

    public void decrementConnectedDevices() {
        connectedDevices.decrementAndGet();
    }

    public long getConnectedDeviceCount() {
        return connectedDevices.get();
    }

    public void incrementMessageProcessed() {
        messageProcessedCounter.increment();
    }

    public void incrementMessageFailed() {
        messageFailedCounter.increment();
    }

    public Timer.Sample startProtocolParseTimer() {
        return Timer.start(meterRegistry);
    }

    public Timer.Sample startDataStorageTimer() {
        return Timer.start(meterRegistry);
    }
}
```

### 10.3 CI/CD流程

#### 10.3.1 GitHub Actions配置 (.github/workflows/ci-cd.yml)

```yaml
name: IoT Data Receiver CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4

    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Cache Maven packages
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run tests
      run: mvn clean test

    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Maven Tests
        path: '**/target/surefire-reports/*.xml'
        reporter: java-junit

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./target/site/jacoco/jacoco.xml

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to production
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USER }}
        key: ${{ secrets.PROD_SSH_KEY }}
        script: |
          cd /opt/iot-data-receiver
          docker-compose pull
          docker-compose up -d
          docker system prune -f
```

## 11. 风险评估与应对

### 11.1 技术风险识别

#### 11.1.1 高风险项

| 风险项 | 风险等级 | 影响范围 | 概率 | 应对措施 |
|--------|----------|----------|------|----------|
| 数据迁移失败 | 高 | 数据完整性 | 中 | 详细迁移计划、回滚方案、数据备份 |
| 协议解析兼容性 | 高 | 设备连接 | 中 | 充分测试、渐进式迁移 |
| 性能回退 | 中 | 系统性能 | 低 | 性能基准测试、监控告警 |
| 依赖版本冲突 | 中 | 系统稳定性 | 中 | 依赖管理、兼容性测试 |

#### 11.1.2 风险应对策略

**数据迁移风险应对**：

```bash
#!/bin/bash
# 数据迁移脚本 (migrate-data.sh)

set -e

# 配置参数
SOURCE_DB="iot_old"
TARGET_DB="iot_new"
BACKUP_DIR="/backup/$(date +%Y%m%d_%H%M%S)"

echo "开始数据迁移流程..."

# 1. 创建备份目录
mkdir -p $BACKUP_DIR

# 2. 备份原始数据
echo "备份原始数据..."
taos -s "use $SOURCE_DB; select * from sensor;" > $BACKUP_DIR/sensor_backup.sql

# 3. 验证备份完整性
BACKUP_COUNT=$(wc -l < $BACKUP_DIR/sensor_backup.sql)
echo "备份记录数: $BACKUP_COUNT"

# 4. 创建新数据库结构
echo "创建新数据库结构..."
taos -s "CREATE DATABASE IF NOT EXISTS $TARGET_DB;"

# 5. 执行数据迁移
echo "执行数据迁移..."
python3 migrate_data.py --source=$SOURCE_DB --target=$TARGET_DB --backup=$BACKUP_DIR

# 6. 验证迁移结果
echo "验证迁移结果..."
TARGET_COUNT=$(taos -s "use $TARGET_DB; select count(*) from insect_killer_data;" | tail -1)
echo "迁移后记录数: $TARGET_COUNT"

# 7. 数据一致性检查
if [ "$BACKUP_COUNT" -eq "$TARGET_COUNT" ]; then
    echo "数据迁移成功！"
else
    echo "数据迁移失败，启动回滚..."
    ./rollback-migration.sh $BACKUP_DIR
    exit 1
fi

echo "数据迁移完成"
```

**协议兼容性测试**：

```java
package com.memory.iot.protocol.compatibility;

import com.memory.iot.protocol.factory.ProtocolParserFactory;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * 协议兼容性测试
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootTest
@DisplayName("协议兼容性测试")
class ProtocolCompatibilityTest {

    @Autowired
    private ProtocolParserFactory parserFactory;

    @Test
    @DisplayName("历史数据包兼容性测试")
    void shouldParseHistoricalPayloads() throws IOException {
        // Given - 从文件加载历史数据包
        List<String> historicalPayloads = Files.readAllLines(
            Paths.get("src/test/resources/historical-payloads.txt"));

        int successCount = 0;
        int failureCount = 0;

        // When & Then
        for (String payload : historicalPayloads) {
            try {
                var parser = parserFactory.selectParser(payload);
                assertThat(parser).isNotNull();

                var result = parser.parse(payload);
                assertThat(result.isSuccess()).isTrue();

                successCount++;
            } catch (Exception e) {
                System.err.printf("解析失败: %s, 错误: %s%n", payload, e.getMessage());
                failureCount++;
            }
        }

        // 兼容性要求：成功率应大于95%
        double successRate = (double) successCount / (successCount + failureCount);
        System.out.printf("兼容性测试结果: 成功 %d, 失败 %d, 成功率: %.2f%%%n",
                         successCount, failureCount, successRate * 100);

        assertThat(successRate).isGreaterThan(0.95);
    }
}
```

### 11.2 运维风险应对

#### 11.2.1 监控告警配置

```yaml
# monitoring/alerts.yml
groups:
- name: iot-data-receiver
  rules:
  - alert: HighErrorRate
    expr: rate(iot_message_failed_total[5m]) / rate(iot_message_processed_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "IoT消息处理错误率过高"
      description: "错误率超过10%，当前值: {{ $value }}"

  - alert: DeviceConnectionDrop
    expr: iot_device_connected < 100
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "设备连接数异常下降"
      description: "当前连接设备数: {{ $value }}"

  - alert: HighMemoryUsage
    expr: process_resident_memory_bytes / 1024 / 1024 > 1500
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "内存使用率过高"
      description: "内存使用: {{ $value }}MB"
```

#### 11.2.2 故障恢复脚本

```bash
#!/bin/bash
# 故障恢复脚本 (recovery.sh)

SERVICE_NAME="iot-data-receiver"
MAX_RETRIES=3
RETRY_INTERVAL=30

check_service_health() {
    curl -f http://localhost:8080/iot/actuator/health >/dev/null 2>&1
    return $?
}

restart_service() {
    echo "重启服务: $SERVICE_NAME"
    docker-compose restart $SERVICE_NAME
    sleep 30
}

for i in $(seq 1 $MAX_RETRIES); do
    if check_service_health; then
        echo "服务健康检查通过"
        exit 0
    else
        echo "服务健康检查失败，尝试重启 ($i/$MAX_RETRIES)"
        restart_service

        if [ $i -eq $MAX_RETRIES ]; then
            echo "服务恢复失败，发送告警"
            # 发送告警通知
            curl -X POST "$ALERT_WEBHOOK_URL" \
                -H "Content-Type: application/json" \
                -d "{\"text\":\"IoT服务恢复失败，需要人工介入\"}"
            exit 1
        fi

        sleep $RETRY_INTERVAL
    fi
done
```

### 11.3 回滚方案

#### 11.3.1 应用回滚

```bash
#!/bin/bash
# 应用回滚脚本 (rollback.sh)

BACKUP_VERSION=${1:-"latest-stable"}
ROLLBACK_TIMEOUT=300

echo "开始回滚到版本: $BACKUP_VERSION"

# 1. 停止当前服务
docker-compose stop iot-data-receiver

# 2. 备份当前配置
cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)

# 3. 恢复到指定版本
sed -i "s/image: .*/image: ghcr.io\/company\/iot-data-receiver:$BACKUP_VERSION/" docker-compose.yml

# 4. 启动服务
docker-compose up -d iot-data-receiver

# 5. 等待服务启动
echo "等待服务启动..."
for i in $(seq 1 $((ROLLBACK_TIMEOUT/10))); do
    if curl -f http://localhost:8080/iot/actuator/health >/dev/null 2>&1; then
        echo "回滚成功，服务已恢复"
        exit 0
    fi
    sleep 10
done

echo "回滚失败，服务未能正常启动"
exit 1
```

---

## 总结

本技术文档详细描述了IoT设备数据接收系统的重构方案，涵盖了从架构设计到部署运维的完整流程。重构后的系统将具备：

1. **更好的可维护性**：清晰的模块划分和职责分离
2. **更强的扩展性**：基于策略模式的协议解析框架
3. **更高的性能**：优化的数据存储和缓存策略
4. **更完善的监控**：全面的指标监控和告警机制
5. **更可靠的部署**：Docker化部署和自动化CI/CD

通过分阶段实施，可以确保重构过程的可控性和系统的稳定性，最终实现20%的性能提升目标。

### 关键成功因素

1. **充分的测试覆盖**：单元测试、集成测试、性能测试全覆盖
2. **渐进式迁移**：分阶段实施，降低风险
3. **完善的监控**：实时监控系统状态，及时发现问题
4. **详细的文档**：完整的技术文档和运维手册
5. **团队协作**：开发、测试、运维团队紧密配合

### 预期收益

- **开发效率提升30%**：清晰的架构和规范的代码
- **系统性能提升20%**：优化的数据存储和处理流程
- **故障恢复时间减少50%**：完善的监控和自动化运维
- **新功能开发周期缩短40%**：可扩展的架构设计

本重构方案为IoT系统的长期发展奠定了坚实的技术基础。

---

## 技术审查总结

### 主要修改内容

#### 1. JSON处理库替换
- **完全替换**：FastJSON2 → Jackson 2.17.2
- **安全性提升**：避免autoType反序列化漏洞
- **生态完善**：Spring Boot官方推荐，社区支持更好
- **迁移指南**：提供详细的代码迁移步骤和注意事项

#### 2. 功能完整性保障
- **设备类型覆盖**：确保11种设备类型100%支持
- **协议兼容性**：所有现有协议标识(9070、9086、9003等)完整保留
- **API兼容性**：HTTP接口(/cmd/1、/cmd/2、/cmd/3等)完全向后兼容
- **核心功能连续性**：设备认证、心跳检测、命令下发等功能无缝迁移

#### 3. 重构范围明确
- **根本性重构**：允许项目结构发生重大变化
- **性能优化**：数据库表结构按设备类型重新设计
- **架构升级**：从三模块升级为六模块微服务架构
- **技术栈现代化**：全面升级到最新稳定版本

#### 4. 风险控制增强
- **分阶段实施**：12周分6个阶段，每阶段都有明确的功能验证
- **回归测试**：每个解析器都要通过历史数据包兼容性测试
- **性能基准**：确保重构后性能不低于现有系统
- **快速回滚**：完整的回滚方案和应急处理流程

### 关键成功因素

1. **功能完整性优先**：确保重构过程中不丢失任何现有功能
2. **安全性提升**：通过Jackson替换解决潜在的安全风险
3. **性能可控**：通过详细的性能测试确保系统性能提升
4. **风险可控**：通过分阶段实施和完整测试降低重构风险

### 预期收益

- **安全性提升100%**：消除FastJSON2的安全风险
- **功能完整性100%**：所有现有功能完整保留
- **性能提升20%**：通过架构优化和数据库重设计
- **可维护性提升50%**：清晰的模块划分和代码规范
- **扩展性提升300%**：基于策略模式的可插拔架构

本次技术审查确保了重构方案的完整性、安全性和可行性，为IoT系统的成功重构提供了坚实的技术保障。