# 设备状态调试指南

## 问题修复说明

针对设备7025000023返回"OFFLINE"状态但实际在线的问题，已实施以下修复：

### 1. 增强的状态检查逻辑

在`ServerUtil.sendCmd()`方法中添加了详细的状态检查和日志记录：

- **ClientInfo为空检查**：记录设备未找到的详细信息
- **设备状态检查**：验证status字段是否为1
- **通道状态检查**：分别检查通道是否为空、是否打开、是否活跃
- **详细日志记录**：每个检查步骤都有对应的日志输出

### 2. 改进的设备上线逻辑

在`TcpSnoopServerHandler.channelRead0()`中优化了设备重连处理：

- **原子性状态更新**：确保ClientInfo状态设置的原子性
- **重连日志记录**：详细记录设备重连过程
- **通道状态验证**：在设置状态前验证通道状态

### 3. 优化的通道关闭逻辑

在`closeChannel()`方法中改进了ClientInfo管理：

- **通道匹配检查**：只有当前通道才能移除ClientInfo
- **重连保护**：设备重连时保留ClientInfo
- **异常处理**：增强异常处理和日志记录

### 4. 新增调试API

添加了两个新的调试接口：

#### 设备状态查询
```
GET /device/status?deviceNo=7025000023
```

返回示例：
```json
{
  "deviceNo": "7025000023",
  "status": "ONLINE",
  "statusCode": 1,
  "onlineTime": "2024-01-15 10:30:00",
  "heartTime": "2024-01-15 10:35:00",
  "channelId": "a1b2c3d4",
  "channelOpen": true,
  "channelActive": true,
  "remoteAddress": "/*************:12345"
}
```

#### 在线设备列表
```
GET /devices/online
```

## 调试步骤

### 1. 检查设备当前状态
```bash
curl "http://localhost:8080/device/status?deviceNo=7025000023"
```

### 2. 发送测试命令
```bash
curl -X POST "http://localhost:8080/cmd/1" \
  -H "Content-Type: application/json" \
  -d '{"deviceNo":"7025000023","cmd":"6823000025706801050070900000018F16"}'
```

### 3. 查看日志输出
关注以下日志关键字：
- `发送命令到设备`
- `设备ClientInfo状态`
- `设备状态检查通过`
- `设备上线成功`
- `设备重连`

### 4. 验证修复效果
- 检查命令响应是否为"SUCCESS"而不是"OFFLINE"
- 验证设备状态API返回正确的在线状态
- 确认日志中没有异常的状态变更

## 可能的根因分析

基于代码分析，"OFFLINE"状态可能由以下原因导致：

1. **竞态条件**：设备重连时状态临时设为0
2. **通道状态不同步**：Netty通道状态与实际设备状态不匹配
3. **ClientInfo被误删**：closeChannel方法过度清理ClientInfo
4. **心跳机制问题**：心跳超时导致设备被标记为离线

## 监控建议

1. **实时监控设备状态变更日志**
2. **定期检查在线设备列表**
3. **监控命令执行成功率**
4. **关注设备重连频率**

修复后，系统将提供更准确的设备状态报告和更详细的调试信息。

## 紧急问题诊断

### 当前问题分析

根据你的测试结果：
```json
{"deviceNo":"7025000023","totalOnlineDevices":159,"message":"设备未找到","timestamp":1754381048704,"status":"NOT_FOUND"}
```

**问题确认**：设备7025000023不在clients映射中，但系统有159个在线设备。

### 立即诊断步骤

#### 1. 检查设备认证信息
```bash
curl "http://127.0.0.1:32086/device/auth?deviceNo=7025000023"
```

#### 2. 搜索相似设备号
```bash
curl "http://127.0.0.1:32086/devices/search?pattern=7025000023"
curl "http://127.0.0.1:32086/devices/search?pattern=702500"
curl "http://127.0.0.1:32086/devices/search?pattern=7025"
```

#### 3. 检查在线设备列表
```bash
curl "http://127.0.0.1:32086/devices/online" | jq '.devices | keys | map(select(contains("7025")))'
```

### 可能的原因和解决方案

#### 原因1：设备号格式问题
- **问题**：设备实际使用的设备号与预期不同
- **检查**：搜索包含"7025"的所有设备
- **解决**：使用正确的设备号

#### 原因2：设备未正确上线
- **问题**：设备没有发送正确的上线消息格式（包含*和#）
- **检查**：查看TcpSnoopServerHandler日志中的"设备上线"相关信息
- **解决**：确保设备发送正确格式的上线消息

#### 原因3：认证失败
- **问题**：Redis中没有设备的认证信息
- **检查**：使用`/device/auth`接口检查
- **解决**：在Redis中添加设备认证信息

#### 原因4：设备被意外移除
- **问题**：设备曾经上线但被closeChannel误删
- **检查**：查看日志中的"closeChannel"和"移除离线设备ClientInfo"
- **解决**：重启设备或使用模拟上线功能

### 临时解决方案

#### 模拟设备上线（仅用于测试）
```bash
curl -X POST "http://127.0.0.1:32086/device/simulate-online" \
  -H "Content-Type: application/json" \
  -d '{"deviceNo":"7025000023","productCode":"TEST_CODE"}'
```

**注意**：这只是模拟状态，没有真实网络连接，命令发送仍会失败。

### 日志监控关键字

在应用日志中搜索以下关键字：
- `deviceNo 7025000023`
- `认证失败`
- `设备上线`
- `closeChannel`
- `移除离线设备ClientInfo`

### 长期解决方案

1. **增强设备上线监控**
2. **改进认证失败处理**
3. **优化ClientInfo生命周期管理**
4. **添加设备状态变更告警**

### 下一步行动

1. 执行上述诊断命令
2. 检查应用日志
3. 确认设备真实状态
4. 根据诊断结果采取相应措施

## 🎯 协议解析问题修复

### 问题确认
经过分析，你的判断是正确的！问题不是设备离线，而是**缺少对杀虫灯实时数据读取命令(9070H)的协议解析**。

### 命令分析
```
命令: 6823000025706801050070900000018F16
- 设备号: 7025000023 ✓
- 控制码: 01H (读取命令) ✓
- 功能码: 9070H (杀虫灯实时数据) ✓
```

### 修复内容

#### 1. 新增杀虫灯数据解析方法
在`CommonUtil.java`中添加了`parseInsectKillerData()`方法：
- 解析杀虫灯工作状态、模式
- 解析环境参数（温度、光照、雨量）
- 解析设备状态（电压、工作时间）
- 提供状态描述信息

#### 2. 完善协议处理逻辑
在`TcpSnoopServerHandler.java`中添加了对功能码`9070`的处理：
- 调用杀虫灯数据解析方法
- 保存数据到TDengine数据库
- 推送解析后的数据

#### 3. 参考采集器实现
对比采集器的`9002`功能码处理逻辑：
- 采集器: `CommonUtil.parseSensorData()` → 功能码9002
- 杀虫灯: `CommonUtil.parseInsectKillerData()` → 功能码9070

### 测试验证

#### 1. 确保设备在线
```bash
# 检查设备状态
curl "http://127.0.0.1:32086/device/status?deviceNo=7025000023"

# 如果设备不在线，先模拟上线
curl -X POST "http://127.0.0.1:32086/device/simulate-online" \
  -H "Content-Type: application/json" \
  -d '{"deviceNo":"7025000023","productCode":"INSECT_KILLER"}'
```

#### 2. 测试杀虫灯命令
```bash
# 发送杀虫灯实时数据读取命令
curl -X POST "http://127.0.0.1:32086/cmd/1" \
  -H "Content-Type: application/json" \
  -d '{"deviceNo":"7025000023","cmd":"6823000025706801050070900000018F16"}'
```

#### 3. 预期结果
- 命令响应: `{"success":true,"message":"SUCCESS",...}`
- 日志输出: 包含"处理杀虫灯实时数据读取命令"
- 数据解析: 解析出工作状态、温度、光照等参数

### 解析数据格式
杀虫灯数据解析后包含以下字段：
```json
{
  "device-type": "杀虫灯",
  "work-status": 1,
  "work-status-desc": "工作中",
  "work-mode": 1,
  "work-mode-desc": "光控模式",
  "current-temperature": 25,
  "current-light": 1200,
  "voltage": 12.5,
  "rain-status-desc": "无雨",
  "total-work-hours": 120.5
}
```

现在系统应该能够正确处理杀虫灯的实时数据读取命令了！
