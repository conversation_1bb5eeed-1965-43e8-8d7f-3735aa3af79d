// 命令解析测试
public class CommandAnalysis {
    public static void main(String[] args) {
        String command = "6823000025706801050070900000018F16";
        
        System.out.println("原始命令: " + command);
        System.out.println("命令长度: " + command.length());
        
        // 按照CommonUtil.divideHeader的逻辑解析
        int index = 0;
        
        // 起始字节
        String startByte = command.substring(index, index + 2);
        index += 2;
        System.out.println("起始字节: " + startByte);
        
        // 设备号 (5字节，需要反转)
        String deviceNoHex = command.substring(index, index + 10);
        String deviceNo = CommonUtil.reverse(deviceNoHex);
        index += 10;
        System.out.println("设备号(hex): " + deviceNoHex + " -> " + deviceNo);
        
        // 第二个起始字节
        String startByte2 = command.substring(index, index + 2);
        index += 2;
        System.out.println("第二起始字节: " + startByte2);
        
        // 控制码
        String controlCode = command.substring(index, index + 2);
        index += 2;
        System.out.println("控制码: " + controlCode);
        
        // 数据长度 (2字节，需要反转)
        String lengthHex = command.substring(index, index + 4);
        String lengthReversed = CommonUtil.reverse(lengthHex);
        int dataLength = Integer.parseInt(lengthReversed, 16);
        index += 4;
        System.out.println("数据长度(hex): " + lengthHex + " -> " + lengthReversed + " -> " + dataLength);
        
        // 功能码 (2字节，需要反转)
        String functionCodeHex = command.substring(index, index + 4);
        String functionCode = CommonUtil.reverse(functionCodeHex);
        index += 4;
        System.out.println("功能码(hex): " + functionCodeHex + " -> " + functionCode);
        
        // 数据域
        int remainingDataLength = (dataLength - 2) * 2; // 减去功能码的2字节
        if (remainingDataLength > 0) {
            String data = command.substring(index, index + remainingDataLength);
            index += remainingDataLength;
            System.out.println("数据域: " + data);
        }
        
        // 校验码
        String checksum = command.substring(index, index + 2);
        index += 2;
        System.out.println("校验码: " + checksum);
        
        // 结束字节
        String endByte = command.substring(index);
        System.out.println("结束字节: " + endByte);
        
        System.out.println("\n=== 解析结果 ===");
        System.out.println("设备号: " + deviceNo);
        System.out.println("控制码: " + controlCode + " (01=读取命令)");
        System.out.println("功能码: " + functionCode + " (7090)");
        System.out.println("数据长度: " + dataLength + " 字节");
    }
}

// 工具类方法
class CommonUtil {
    public static String reverse(String str) {
        if (str.length() % 2 != 0) str = "0" + str;
        char[] a = str.toCharArray();
        StringBuilder b = new StringBuilder();
        for (int i = a.length; i > 0; i -= 2) {
            b.append(a[i - 2]);
            b.append(a[i - 1]);
        }
        return b.toString();
    }
}
